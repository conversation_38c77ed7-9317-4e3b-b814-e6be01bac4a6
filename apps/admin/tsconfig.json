{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": "", "paths": {"@/*": ["src/*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "@workspace/investment-api/*": ["../../packages/investment-api/src/*"]}, "plugins": [{"name": "next"}]}, "include": ["svgr.d.ts", "next-env.d.ts", "next.config.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}