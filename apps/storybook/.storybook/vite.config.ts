import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

export default defineConfig({
  plugins: [
    svgr({
      svgrOptions: {
        exportType: "default",
        ref: true,
        svgo: true,
        titleProp: true,
      },
      include: "**/*.svg",
    }),
    react({
      jsxImportSource: "react",
      jsxRuntime: "automatic",
    }),
  ],
  resolve: {
    alias: {
      "@workspace/ui": path.resolve(__dirname, "../../../packages/ui/src"),
      "@workspace/web": path.resolve(__dirname, "../../../apps/web/src"),
      "@workspace/admin": path.resolve(__dirname, "../../../apps/admin/src"),
      "@": path.resolve(__dirname, "../../../apps/web/src"),
      // "@": path.resolve(__dirname, "../"),
    },
  },
  optimizeDeps: {
    include: ["@workspace/ui", "@workspace/web", "@workspace/admin"],
  },
  assetsInclude: ["**/*.woff", "**/*.woff2", "**/*.ttf", "**/*.otf"],
});
