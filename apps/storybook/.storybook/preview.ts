import type { Preview } from "@storybook/react";
// import "@workspace/ui/globals.css";
import "../../../packages/ui/src/styles/globals.css";

import "../font.css";
import "./storybook.css";

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    // controls: {
    //   matchers: {
    //     color: /(background|color)$/i,
    //     date: /Date$/i,
    //   },
    // },
    // backgrounds: {
    //   default: "light",
    //   values: [
    //     { name: "light", value: "#28282C" },
    //     { name: "dark", value: "#28282C" },
    //   ],
    // },
  },
  globalTypes: {
    theme: {
      description: "Global theme for components",
      defaultValue: "light",
      toolbar: {
        title: "Theme",
        icon: "circlehollow",
        items: ["light", "dark"],
        dynamicTitle: true,
      },
    },
  },
  decorators: [
    (Story, context) => {
      const theme = context.globals.theme;
      document.body.className = theme === "dark" ? "dark" : "";
      return Story();
    },
  ],
};

export default preview;
