@font-face {
  font-family: "yekan Fallback";
  src: local("Arial");
  ascent-override: 100.89%;
  descent-override: 67.26%;
  line-gap-override: 5.6%;
  size-adjust: 89.21%;
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 100;
  src: url("/fonts/YekanBakhFaNum-Thin.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 300;
  src: url("/fonts/YekanBakhFaNum-Light.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 400;
  src: url("/fonts/YekanBakhFaNum-Regular.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 600;
  src: url("/fonts/YekanBakhFaNum-SemiBold.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 700;
  src: url("/fonts/YekanBakhFaNum-Bold.woff") format("woff");
}
@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 800;
  src: url("/fonts/YekanBakhFaNum-ExtraBold.woff") format("woff");
}
@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 900;
  src: url("/fonts/YekanBakhFaNum-Black.woff") format("woff");
}

@font-face {
  font-family: yekan-bakh;
  font-style: normal;
  font-weight: 950;
  src: url("/fonts/YekanBakhFaNum-ExtraBlack.woff") format("woff");
}
