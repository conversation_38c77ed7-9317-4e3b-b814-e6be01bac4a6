FROM nexus.otcsaba.ir:8083/node:20-alpine AS base

RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Configure npm registry globally for all stages
RUN npm config set registry https://nexus.otcsaba.ir/repository/npm/ && \
    npm config set strict-ssl false && \
    npm config get registry

# Install pnpm globally using npm (more reliable than corepack)
RUN npm install -g pnpm@10.4.1 turbo http-server

# ---------------------------------------------------------------------------- #
#                               builder stage                                  #
# ---------------------------------------------------------------------------- #
FROM base AS builder
WORKDIR /app
COPY . .
RUN turbo prune storybook --docker

# ---------------------------------------------------------------------------- #
#                              installer stage                                 #
# ---------------------------------------------------------------------------- #
# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
WORKDIR /app
COPY --from=builder /app/out/json/ .
RUN pnpm install
COPY --from=builder /app/out/full/ .
RUN pnpm turbo build --filter=storybook

# ---------------------------------------------------------------------------- #
#                                 runner stage                                 #
# ---------------------------------------------------------------------------- #
# Production stage - serve static files with nginx
FROM base AS runner
WORKDIR /app
COPY --from=installer /app/package.json ./package.json
COPY --from=installer /app/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=installer /app/turbo.json ./turbo.json

COPY --from=installer /app/node_modules ./node_modules

# Copy the built storybook static files
COPY --from=installer /app/apps/storybook/storybook-static ./apps/storybook/storybook-static
COPY --from=installer /app/apps/storybook/node_modules ./apps/storybook/node_modules
COPY --from=installer /app/apps/storybook/package.json ./apps/storybook/package.json


COPY --from=installer /app/packages ./packages


# Expose the port where you want to serve Storybook (e.g., 3000)
EXPOSE 3000

# Serve the generated storybook-static folder
CMD ["pnpm", "--filter", "storybook", "start"]

