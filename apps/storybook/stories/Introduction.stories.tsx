import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React from "react";

const meta: Meta = {
  title: "Introduction",
  parameters: {
    layout: "centered",
  },
};

export default meta;
type Story = StoryObj;

export const Introduction: Story = {
  render: (): React.ReactNode => (
    <div className="container mx-auto w-full mt-20">
      <div className="flex items-center justify-center mb-4">
        <div className="text-center flex flex-col items-center gap-2">
          <svg
            width="34"
            height="48"
            viewBox="0 0 34 48"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_7102_237)">
              <path
                opacity="0.65"
                d="M0.000387137 34V11.333C-0.0230355 8.35379 1.01722 5.48625 2.89237 3.36098C4.76753 1.23572 7.32402 0.0267619 9.99961 0V22.667C10.023 25.6462 8.98278 28.5138 7.10763 30.639C5.23247 32.7643 2.67598 33.9732 0.000387137 34Z"
                fill="#1477FF"
              />
              <path
                opacity="0.65"
                d="M11 34H0.000425868V22.9994C-0.02534 20.108 1.11899 17.3248 3.18173 15.2621C5.24448 13.1994 8.05673 12.026 11 12H21.9996V22.9994C22.0253 25.8909 20.881 28.6741 18.8183 30.7368C16.7555 32.7995 13.9433 33.974 11 34Z"
                fill="#1477FF"
              />
              <path
                opacity="0.65"
                d="M22.6661 34H0.000438793C-0.0261091 31.3712 1.15295 28.841 3.27831 26.9657C5.40366 25.0904 8.30127 24.0236 11.3339 24H33.9996C34.0261 26.6288 32.8471 29.159 30.7217 31.0343C28.5963 32.9096 25.6987 33.9764 22.6661 34Z"
                fill="#1477FF"
              />
              <path
                opacity="0.65"
                d="M4.99948 48H9.9998V43C10.0117 41.6857 9.49162 40.4205 8.55409 39.4829C7.61656 38.5452 6.33832 38.0118 5.00052 38H0.000198156V43C-0.0116515 44.3143 0.50838 45.5795 1.44591 46.5171C2.38344 47.4548 3.66168 47.9882 4.99948 48Z"
                fill="#1477FF"
              />
            </g>
            <defs>
              <clipPath id="clip0_7102_237">
                <rect width="34" height="48" fill="white" />
              </clipPath>
            </defs>
          </svg>

          <p className="text-gray-500 text-xs">خدمات مدیریت صبا تامین</p>
          <div className="text-xl font-medium text-black">
            دیزاین سیستم نارنگی
          </div>
        </div>
      </div>
    </div>
  ),
};
