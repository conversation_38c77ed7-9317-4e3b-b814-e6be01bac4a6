{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "orval": "orval --config ./src/services/api/auto-generated/orval.config.ts && pnpm lint --fix "}, "dependencies": {"@tailwindcss/postcss": "^4.0.8", "@tanstack/react-query": "^5.79.0", "@workspace/ui": "workspace:*", "@workspace/investment-api": "workspace:*", "axios": "^1.9.0", "js-cookie": "^3.0.5", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.0.8"}, "devDependencies": {"@storybook/react": "^8.6.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "orval": "^7.9.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.7.3"}}