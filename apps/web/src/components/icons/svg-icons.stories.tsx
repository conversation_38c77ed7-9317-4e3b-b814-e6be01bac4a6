import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipTrigger,
} from "@workspace/ui/components/tooltip";
import { Check, Copy } from "lucide-react";
import { FC, SVGProps, useState } from "react";

const meta: Meta = {
  title: "Web/SVG Icons",
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "Test story to verify that SVG icons are properly imported and rendered using SVGR.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div className="container max-w-4xl p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj;

// Dynamically import all SVG icons from the assets/icons folder
const iconModules = import.meta.glob("../../assets/icons/*.svg", {
  eager: true,
  import: "default",
}) as Record<string, FC<SVGProps<SVGElement>>>;

// Function to get all icons with their metadata
function getAllIcons() {
  return Object.entries(iconModules).map(([path, component]) => {
    // Extract filename from path (e.g., "../../assets/icons/arrow-down.svg" -> "arrow-down")
    const filename = path.split("/").pop()?.replace(".svg", "") || "";

    // Convert filename to PascalCase component name
    const componentName = filename
      .split(/[-_]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join("");

    // Convert filename to readable name
    const name = filename
      .split(/[-_]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    return {
      name,
      filename,
      componentName,
      component,
    };
  });
}

// Copy button component
function CopyButton({ text, tooltip }: { text: string; tooltip: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          onClick={handleCopy}
          className="bg-background/80 hover:bg-background border-border/50 hover:border-border absolute top-2 right-2 rounded-md border p-1 opacity-0 transition-all duration-200 group-hover:opacity-100"
        >
          {copied ? (
            <Check className="h-3 w-3 text-green-600" />
          ) : (
            <Copy className="text-muted-foreground h-3 w-3" />
          )}
        </button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{copied ? "Copied!" : tooltip}</p>
      </TooltipContent>
    </Tooltip>
  );
}

function IconGrid() {
  // Get all icons
  const icons = getAllIcons();

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">SVG Icons</h3>
        <p className="text-muted-foreground mb-4">
          Automatically displaying all SVG icons from assets/icons folder (
          {icons.length} icons found)
        </p>

        <p>
          Click copy to copy the import statement and JSX usage for each icon.
        </p>
      </div>
      <div className="wrap flex flex-wrap gap-4">
        {icons.map(
          ({ name, filename, componentName, component: IconComponent }) => {
            const importStatement = `import ${componentName} from "@/assets/icons/${filename}.svg"`;
            const jsxUsage = `<${componentName} />`;
            const copyText = `${importStatement}\n\n${jsxUsage}`;

            return (
              <div
                key={filename}
                className="group relative flex flex-col items-center space-y-2 rounded-lg border p-4 transition-shadow hover:shadow-md"
              >
                <CopyButton text={copyText} tooltip="Copy import and usage" />
                <IconComponent className="text-text-nautral-default h-8 w-8" />
                <div className="text-center">
                  <span className="text-sm font-medium">{name}</span>
                </div>
              </div>
            );
          },
        )}
      </div>
    </div>
  );
}

export const AllIcons: Story = {
  render: () => <IconGrid />,
};
