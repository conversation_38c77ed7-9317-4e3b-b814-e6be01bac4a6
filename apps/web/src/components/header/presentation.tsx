import User from "@/assets/icons/User.svg";
import Question from "@/assets/icons/question.svg";
import { ReactNode } from "react";

function Header({
  title,
  subTitle,
  startAdornment,
  endAdornment,
}: {
  title?: string;
  subTitle?: string;
  startAdornment?: ReactNode;
  endAdornment?: ReactNode;
}) {
  return (
    <div className="bg-surface-nautral-default-4 flex items-center justify-between p-4">
      <div className="flex items-center gap-2">
        {startAdornment || (
          <div className="bg-surface-nautral-default-3 rounded-sm p-2">
            <User className="text-icon-neutral-default size-4" />
          </div>
        )}

        <div className="flex flex-col">
          <div className="text-text-nautral-default text-[10px] font-bold">
            {title}
          </div>
          <div className="text-text-warning-default text-[10px] font-normal">
            {subTitle}
          </div>
        </div>
      </div>

      {endAdornment || (
        <div className="bg-surface-nautral-default-3 rounded-sm p-2">
          <Question className="text-icon-neutral-default size-4" />
        </div>
      )}
    </div>
  );
}

export default Header;
