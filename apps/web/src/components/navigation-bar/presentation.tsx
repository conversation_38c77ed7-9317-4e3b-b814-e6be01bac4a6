import Graph from "@/assets/icons/graph.svg";
import Growt from "@/assets/icons/Growt.svg";
import HomeSelected from "@/assets/icons/Home-selected.svg";

function NavigationBar() {
  return (
    <div className="fixed right-0 bottom-0 left-0 w-full px-2 pb-2">
      <div className="bg-surface-nautral-default-4 shadow-navigation flex w-full items-center justify-around rounded-2xl px-2 py-3">
        <div className="flex flex-col items-center justify-center gap-1">
          <Growt className="text-text-nautral-secondary size-6" />
          <div className="text-text-nautral-secondary text-[10px] font-normal">
            صندوق‌ها
          </div>
        </div>

        <div
          // className="p-1.5"
          style={
            {
              // borderRadius: "15.33px",
              // boxShadow: "0px 1.1px 1.1px 0px #DAB06640 inset",
            }
          }
        >
          <HomeSelected className="" />
        </div>

        <div className="flex flex-col items-center justify-center gap-1">
          <Graph className="text-text-nautral-secondary size-6" />
          <div className="text-text-nautral-secondary text-[10px] font-normal">
            دارایی من
          </div>
        </div>
      </div>
    </div>
  );
}

export default NavigationBar;
