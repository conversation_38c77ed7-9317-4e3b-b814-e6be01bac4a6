import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import NavigationBar from "./presentation";

const meta: Meta<typeof NavigationBar> = {
  title: "Web/NavigationBar",
  component: NavigationBar,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="relative container max-w-sm p-8" dir="rtl">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["fill", "outline", "noFrame"],
      control: { type: "radio" },
    },
    color: {
      options: ["default", "warning", "destructive", "success"],
      control: { type: "radio" },
    },
    size: {
      options: ["xs", "sm", "md", "lg"],
      control: { type: "radio" },
    },
    disabled: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof NavigationBar>;

export const Primary: Story = {
  args: {
    children: "Header",
  },
};
