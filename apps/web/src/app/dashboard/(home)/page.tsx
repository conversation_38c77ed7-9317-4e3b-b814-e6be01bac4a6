"use client";

import {
  useCaptchaRequestTextCaptcha,
  useCaptchaRequestTextCaptcha2,
  useCaptchaWhichCaptchaIsActive,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { CaptchaInput } from "@workspace/ui/components/captcha-input/captcha-input";
import { CaptchaTypeEnum } from "@workspace/investment-api/auto-generated/models";

function Page() {
  return (
    <div>
      <CaptchaInput
        useCaptchaWhichCaptchaIsActive={useCaptchaWhichCaptchaIsActive}
        useCaptchaRequestTextCaptcha={useCaptchaRequestTextCaptcha}
        onCaptchaChange={(dd) => {
          console.log("scascas", dd);
        }}
        googleRecaptchaSiteKey="6LeXTUMqAAAAAK_ttq5gu-5aYvpbxLouSE31AD8s"
        className="mb-4"
      />
    </div>
  );
}

export default Page;
