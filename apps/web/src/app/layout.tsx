import localFont from "next/font/local";

import { Providers } from "@/components/providers";
import "../styles/global.css";

const yekan = localFont({
  src: [
    {
      path: "../assets/fonts/YekanBakhFaNum-Thin.woff",
      weight: "100",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Light.woff",
      weight: "300",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Regular.woff",
      weight: "400",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-SemiBold.woff",
      weight: "600",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Bold.woff",
      weight: "700",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-ExtraBold.woff",
      weight: "800",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Black.woff",
      weight: "900",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-ExtraBlack.woff",
      weight: "950",
      style: "normal",
    },
  ],
  variable: "--font-yekan",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" suppressHydrationWarning dir="rtl">
      <body className={`${yekan.variable} font-yekan antialiased`}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
