<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_iiii_3008_3742)">
        <path
            d="M0.0182937 17.5453C0.0213428 17.5941 0.0335396 17.6399 0.0426868 17.6856C0.0487849 17.7161 0.0487847 17.7466 0.0579319 17.774C0.0701282 17.8136 0.0853734 17.8533 0.100619 17.8899C0.106717 17.9051 0.112815 17.9234 0.118914 17.9387C1.25012 20.3474 8.96427 20.4389 17.7517 18.085C26.4659 15.7494 31.5762 11.9015 31.9726 9.21222C31.9726 9.20307 31.9756 9.19697 31.9756 9.18783C31.9909 9.08416 32 8.98049 32 8.87987V6.21193C32 6.16925 31.9969 6.12961 31.9878 6.08997C31.9817 6.06253 31.9787 6.03509 31.9695 6.01069C31.0914 3.45557 23.24 3.31227 14.2757 5.71493C10.876 6.62661 8.02211 7.76086 5.78409 8.96829V13.2248C8.02211 12.0174 10.873 10.8801 14.2757 9.96839C20.3282 8.34628 25.8714 7.88587 29.1064 8.54142C26.6946 10.4044 22.8375 12.2826 17.7486 13.6486C11.684 15.2738 6.13168 15.7342 2.90271 15.0695C1.17999 14.7676 0.628108 13.2888 2.27461 11.3222C1.2989 12.1698 0.628108 13.0053 0.289661 13.7828C0.289661 13.7828 0.289661 13.7858 0.289661 13.7889C0.207336 13.981 0.140257 14.167 0.0975702 14.353C0.0975702 14.3651 0.0914712 14.3773 0.0884222 14.3895C0.0731768 14.4627 0.0579323 14.5359 0.0487851 14.6091C0.0487851 14.6304 0.0426862 14.6487 0.0396371 14.6701C0.0304899 14.7524 0.024392 14.8378 0.024392 14.9201L0 17.2984H0.00914684C0.00914684 17.3807 0.00914647 17.463 0.0182937 17.5423V17.5453Z"
            fill="url(#paint0_linear_3008_3742)" />
        <path
            d="M31.9996 12.9991C31.9448 13.1211 31.6277 13.7126 31.5453 13.8315C29.8866 16.2251 25.2673 19.14 17.7483 21.1554C8.6712 23.5886 0.740561 23.4087 0.0301272 20.759V22.4208L0.00268555 24.9576C0.00268555 25.1192 0.0118332 25.2808 0.0484221 25.4241C0.0667165 25.482 0.08501 25.5369 0.109403 25.5918C0.109403 25.5918 0.109403 25.5949 0.109403 25.5979C0.109403 25.601 0.109403 25.5979 0.109403 25.601C1.22841 28.0189 8.95171 28.1103 17.7452 25.7534C26.8223 23.3202 31.9966 19.2467 31.9966 16.5513V13.9291C31.9966 13.6273 31.9966 12.9961 31.9966 12.9961L31.9996 12.9991Z"
            fill="url(#paint1_linear_3008_3742)" />
    </g>
    <defs>
        <filter id="filter0_iiii_3008_3742" x="-1" y="0" width="34" height="33"
            filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dy="1" />
            <feGaussianBlur stdDeviation="2" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix type="matrix"
                values="0 0 0 0 0.854902 0 0 0 0 0.690196 0 0 0 0 0.4 0 0 0 1 0" />
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow_3008_3742" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dy="1" />
            <feGaussianBlur stdDeviation="0.5" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
            <feBlend mode="normal" in2="effect1_innerShadow_3008_3742"
                result="effect2_innerShadow_3008_3742" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dx="-1" />
            <feGaussianBlur stdDeviation="0.5" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
            <feBlend mode="normal" in2="effect2_innerShadow_3008_3742"
                result="effect3_innerShadow_3008_3742" />
            <feColorMatrix in="SourceAlpha" type="matrix"
                values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
            <feOffset dx="1" />
            <feGaussianBlur stdDeviation="0.5" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
            <feBlend mode="normal" in2="effect3_innerShadow_3008_3742"
                result="effect4_innerShadow_3008_3742" />
        </filter>
        <linearGradient id="paint0_linear_3008_3742" x1="32" y1="11.8995" x2="0" y2="11.8995"
            gradientUnits="userSpaceOnUse">
            <stop offset="0.3" stop-color="#DAB066" />
            <stop offset="0.45" stop-color="#E9D0A2" />
            <stop offset="0.62" stop-color="#DEB875" />
        </linearGradient>
        <linearGradient id="paint1_linear_3008_3742" x1="31.9996" y1="20.2325" x2="0.00268555"
            y2="20.2325" gradientUnits="userSpaceOnUse">
            <stop offset="0.3" stop-color="#DAB066" />
            <stop offset="0.45" stop-color="#E9D0A2" />
            <stop offset="0.62" stop-color="#DEB875" />
        </linearGradient>
    </defs>
</svg>