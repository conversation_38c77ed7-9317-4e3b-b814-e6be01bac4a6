FROM nexus.otcsaba.ir:8083/node:20-alpine AS base

RUN apk update
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Configure npm registry globally for all stages
RUN npm config set registry https://nexus.otcsaba.ir/repository/npm/ && \
    npm config set strict-ssl false && \
    npm config get registry

# Install pnpm globally using npm (more reliable than corepack)
RUN npm install -g pnpm@10.4.1 turbo

# ---------------------------------------------------------------------------- #
#                               builder stage                                  #
# ---------------------------------------------------------------------------- #
FROM base AS builder
WORKDIR /app
COPY . .
RUN turbo prune web --docker

# ---------------------------------------------------------------------------- #
#                              installer stage                                 #
# ---------------------------------------------------------------------------- #
# Add lockfile and package.json's of isolated subworkspace
FROM base AS installer
WORKDIR /app
COPY --from=builder /app/out/json/ .
RUN pnpm install
COPY --from=builder /app/out/full/ .
RUN pnpm turbo build --filter=web

# ---------------------------------------------------------------------------- #
#                                 runner stage                                 #
# ---------------------------------------------------------------------------- #
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Don't run production as root
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy workspace configuration files (required for pnpm workspaces)
COPY --from=installer --chown=nextjs:nodejs /app/package.json ./package.json
COPY --from=installer --chown=nextjs:nodejs /app/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=installer --chown=nextjs:nodejs /app/turbo.json ./turbo.json

# Copy the root node_modules (where pnpm hoists dependencies)
COPY --from=installer --chown=nextjs:nodejs /app/node_modules ./node_modules

# Copy the built web application
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next ./apps/web/.next
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/package.json ./apps/web/package.json
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/node_modules ./apps/web/node_modules

# Copy any workspace packages that web depends on (like @workspace/ui)
COPY --from=installer --chown=nextjs:nodejs /app/packages ./packages

USER nextjs

EXPOSE 3000

# Stay in root directory where pnpm workspace and node_modules are located
# Use pnpm to run the start script which will handle the workspace correctly
CMD ["pnpm", "--filter", "web", "start"]