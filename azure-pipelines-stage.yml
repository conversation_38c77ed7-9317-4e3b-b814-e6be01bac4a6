---
trigger:
  - stage
pool:
  name: default
stages:
  - stage: Build
    displayName: Build stage
    jobs:
      - job: Build
        steps:
          - script: docker login --username test --password test nexus.otcsaba.ir:8082
          - script: docker build -f ./apps/web/Dockerfile -t frontend:$(Build.BuildId) .
          - script: docker tag frontend:$(Build.BuildId)
              nexus.otcsaba.ir:8082/investment/stage/frontend:$(Build.BuildId)
          - script: docker push nexus.otcsaba.ir:8082/investment/stage/frontend:$(Build.BuildId)
  - stage: Deploy
    displayName: Deploy stage
    jobs:
      - job: Deploy
        steps:
          - task: SSH@0
            inputs:
              sshEndpoint: InvestmentStage
              runOptions: commands
              commands: >
                docker pull nexus.otcsaba.ir:8082/investment/stage/frontend:$(Build.BuildId);
                docker service update --image nexus.otcsaba.ir:8082/investment/stage/frontend:$(Build.BuildId) frontend --with-registry-auth
              readyTimeout: "20000"
            timeoutInMinutes: 20
