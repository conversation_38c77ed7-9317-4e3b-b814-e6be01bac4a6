declare module "*.svg" {
  import { FC, SVGProps } from "react";
  const content: FC<SVGProps<SVGElement>>;
  export default content;
}

declare module "*.svg?url" {
  const content: any;
  export default content;
}

// Add Vite types for import.meta.glob
interface ImportMetaEnv {
  readonly [key: string]: any;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
  glob<T = any>(
    pattern: string,
    options?: {
      eager?: boolean;
      import?: string;
      as?: string;
    },
  ): Record<string, T>;
}
