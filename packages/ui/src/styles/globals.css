@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";
@import "./design-system-variables.css";
@import "./shadcn.css";
@import "./tailwind-theme.css";

@import "tw-animate-css";

/* All Colors */
:root {
  /* Light */
  /* color */
  --azure-radiance-100: hsl(202 100% 92%);
  --azure-radiance-200: hsl(201 100% 86%);
  --azure-radiance-300: hsl(199 100% 77%);
  --azure-radiance-400: hsl(201 100% 67%);
  --azure-radiance-50: hsl(201 100% 97%);
  --azure-radiance-500: hsl(206 100% 59%);
  --azure-radiance-600: hsl(210 95% 51%);
  --azure-radiance-700: hsl(213 87% 48%);
  --azure-radiance-800: hsl(213 74% 33%);
  --azure-radiance-900: hsl(218 46% 22%);
  --azure-radiance-950: hsl(219 45% 15%);
  --azure-radiance-975: hsl(219 100% 6%);
  --background-nautral-body: var(--gray-scale-80);
  --border-error-default: var(--carnation-500);
  --border-error-default-2: var(--carnation-400);
  --border-nautral-default: var(--gray-scale-100);
  --border-nautral-disable: var(--gray-scale-100);
  --border-nautral-divider: var(--gray-scale-100);
  --border-nautral-hover: var(--gray-scale-400);
  --border-primary-default: var(--azure-radiance-600);
  --border-success-default: var(--jade-600);
  --border-warning-default: var(--saffron-500);
  --button-error-default: var(--carnation-500);
  --button-error-hover: var(--carnation-600);
  --button-error-press: var(--carnation-600);
  --button-neutral-default: var(--gray-scale-950);
  --button-neutral-disable: var(--gray-scale-100);
  --button-neutral-hover: var(--gray-scale-800);
  --button-neutral-press: var(--gray-scale-800);
  --button-primary-default: var(--azure-radiance-600);
  --button-primary-hover: var(--azure-radiance-700);
  --button-primary-press: var(--azure-radiance-700);
  --button-success-default: var(--jade-600);
  --button-success-hover: var(--jade-700);
  --button-success-press: var(--jade-700);
  --button-warning-default: var(--saffron-600);
  --button-warning-hover: var(--saffron-700);
  --button-warning-press: var(--saffron-700);
  --carnation-100: hsl(0 100% 94%);
  --carnation-200: hsl(0 100% 89%);
  --carnation-300: hsl(0 100% 81%);
  --carnation-400: hsl(0 100% 68%);
  --carnation-50: hsl(0 100% 97%);
  --carnation-500: hsl(0 93% 60%);
  --carnation-600: hsl(0 79% 51%);
  --carnation-700: hsl(0 81% 42%);
  --carnation-800: hsl(0 69% 31%);
  --carnation-900: hsl(0 82% 15%);
  --carnation-950: hsl(0 81% 6%);
  --gray-scale-100: hsl(0 0 74%);
  --gray-scale-20: hsl(0 0 99%);
  --gray-scale-200: hsl(0 0 52%);
  --gray-scale-300: hsl(0 0 40%);
  --gray-scale-40: hsl(0 0 96%);
  --gray-scale-400: hsl(0 0 33%);
  --gray-scale-500: hsl(0 0 24%);
  --gray-scale-60: hsl(0 0 96%);
  --gray-scale-600: hsl(240 4% 21%);
  --gray-scale-700: hsl(240 5% 16%);
  --gray-scale-80: hsl(0 0 94%);
  --gray-scale-800: hsl(240 5% 15%);
  --gray-scale-900: hsl(240 5% 13%);
  --gray-scale-950: hsl(0 0 9%);
  --icon-error-default: var(--carnation-500);
  --icon-error-default-2: var(--carnation-400);
  --icon-error-hover: var(--carnation-600);
  --icon-neutral-black: var(--gray-scale-800);
  --icon-neutral-default: var(--gray-scale-800);
  --icon-neutral-disable: var(--gray-scale-200);
  --icon-neutral-hover: var(--gray-scale-300);
  --icon-neutral-secondary: var(--gray-scale-300);
  --icon-neutral-white: var(--gray-scale-80);
  --icon-primary-default: var(--azure-radiance-600);
  --icon-primary-hover: var(--azure-radiance-700);
  --icon-success-default: var(--jade-600);
  --icon-success-hover: var(--jade-700);
  --icon-warning-default: var(--saffron-500);
  --icon-warning-hover: var(--saffron-600);
  --jade-100: hsl(155 95% 93%);
  --jade-200: hsl(154 87% 85%);
  --jade-300: hsl(154 84% 73%);
  --jade-400: hsl(154 76% 58%);
  --jade-50: hsl(152 88% 97%);
  --jade-500: hsl(155 77% 45%);
  --jade-600: hsl(155 84% 36%);
  --jade-700: hsl(155 79% 29%);
  --jade-800: hsl(156 67% 20%);
  --jade-900: hsl(157 88% 10%);
  --jade-950: hsl(180 100% 6%);
  --saffron-100: hsl(53 90% 88%);
  --saffron-200: hsl(52 92% 77%);
  --saffron-300: hsl(49 90% 64%);
  --saffron-400: hsl(47 88% 53%);
  --saffron-50: hsl(54 83% 95%);
  --saffron-500: hsl(44 86% 47%);
  --saffron-600: hsl(40 88% 40%);
  --saffron-700: hsl(34 85% 33%);
  --saffron-800: hsl(27 66% 26%);
  --saffron-900: hsl(25 78% 14%);
  --saffron-950: hsl(25 80% 6%);
  --surface-error-default: var(--carnation-50);
  --surface-nautral-active: var(--gray-scale-40);
  --surface-nautral-default-1: var(--gray-scale-20);
  --surface-nautral-default-2: var(--gray-scale-80);
  --surface-nautral-default-3: var(--gray-scale-20);
  --surface-nautral-default-4: var(--gray-scale-40);
  --surface-nautral-disable: var(--gray-scale-200);
  --surface-nautral-modal-cover: hsl(0 0 70% / 0.5);
  --surface-nautral-transparent: hsl(0 0 73% / 0.3);
  --surface-primary-default: var(--azure-radiance-50);
  --surface-success-default: var(--jade-50);
  --surface-warning-default: var(--saffron-50);
  --text-error-default: var(--carnation-500);
  --text-error-default-2: var(--carnation-400);
  --text-nautral-black: var(--gray-scale-800);
  --text-nautral-default: var(--gray-scale-800);
  --text-nautral-disable: var(--gray-scale-200);
  --text-nautral-secondary: var(--gray-scale-300);
  --text-nautral-white: var(--gray-scale-80);
  --text-primary-default: var(--azure-radiance-600);
  --text-primary-hover: var(--azure-radiance-700);
  --text-success-default: var(--jade-600);
  --text-warning-default: var(--saffron-600);
}

.dark {
  /* Dark */
  /* color */
  --azure-radiance-100: hsl(202 100% 92%);
  --azure-radiance-200: hsl(201 100% 86%);
  --azure-radiance-300: hsl(199 100% 77%);
  --azure-radiance-400: hsl(201 100% 67%);
  --azure-radiance-50: hsl(201 100% 97%);
  --azure-radiance-500: hsl(206 100% 59%);
  --azure-radiance-600: hsl(210 95% 51%);
  --azure-radiance-700: hsl(213 87% 48%);
  --azure-radiance-800: hsl(213 74% 33%);
  --azure-radiance-900: hsl(218 46% 22%);
  --azure-radiance-950: hsl(219 45% 15%);
  --azure-radiance-975: hsl(219 100% 6%);
  --background-nautral-body: var(--gray-scale-700);
  --border-error-default: var(--carnation-500);
  --border-error-default-2: var(--carnation-300);
  --border-nautral-default: var(--gray-scale-200);
  --border-nautral-disable: var(--gray-scale-400);
  --border-nautral-divider: var(--gray-scale-300);
  --border-nautral-hover: var(--gray-scale-100);
  --border-primary-default: var(--azure-radiance-600);
  --border-success-default: var(--jade-600);
  --border-warning-default: var(--saffron-400);
  --button-error-default: var(--carnation-600);
  --button-error-hover: var(--carnation-700);
  --button-error-press: var(--carnation-700);
  --button-neutral-default: var(--gray-scale-80);
  --button-neutral-disable: var(--gray-scale-200);
  --button-neutral-hover: var(--gray-scale-100);
  --button-neutral-press: var(--gray-scale-100);
  --button-primary-default: var(--azure-radiance-600);
  --button-primary-hover: var(--azure-radiance-700);
  --button-primary-press: var(--azure-radiance-700);
  --button-success-default: var(--jade-600);
  --button-success-hover: var(--jade-700);
  --button-success-press: var(--jade-700);
  --button-warning-default: var(--saffron-600);
  --button-warning-hover: var(--saffron-700);
  --button-warning-press: var(--saffron-700);
  --carnation-100: hsl(0 100% 94%);
  --carnation-200: hsl(0 100% 89%);
  --carnation-300: hsl(0 100% 81%);
  --carnation-400: hsl(0 100% 68%);
  --carnation-50: hsl(0 100% 97%);
  --carnation-500: hsl(0 93% 60%);
  --carnation-600: hsl(0 79% 51%);
  --carnation-700: hsl(0 81% 42%);
  --carnation-800: hsl(0 69% 31%);
  --carnation-900: hsl(0 82% 15%);
  --carnation-950: hsl(0 81% 6%);
  --gray-scale-100: hsl(0 0 74%);
  --gray-scale-20: hsl(0 0 99%);
  --gray-scale-200: hsl(0 0 52%);
  --gray-scale-300: hsl(0 0 40%);
  --gray-scale-40: hsl(0 0 96%);
  --gray-scale-400: hsl(0 0 33%);
  --gray-scale-500: hsl(0 0 24%);
  --gray-scale-60: hsl(0 0 96%);
  --gray-scale-600: hsl(240 4% 21%);
  --gray-scale-700: hsl(240 5% 16%);
  --gray-scale-80: hsl(0 0 94%);
  --gray-scale-800: hsl(240 5% 15%);
  --gray-scale-900: hsl(240 5% 13%);
  --gray-scale-950: hsl(0 0 9%);
  --icon-error-default: var(--carnation-500);
  --icon-error-default-2: var(--carnation-300);
  --icon-error-hover: var(--carnation-600);
  --icon-neutral-black: var(--gray-scale-800);
  --icon-neutral-default: var(--gray-scale-80);
  --icon-neutral-disable: var(--gray-scale-400);
  --icon-neutral-hover: var(--gray-scale-100);
  --icon-neutral-secondary: var(--gray-scale-100);
  --icon-neutral-white: var(--gray-scale-80);
  --icon-primary-default: var(--azure-radiance-600);
  --icon-primary-hover: var(--azure-radiance-700);
  --icon-success-default: var(--jade-600);
  --icon-success-hover: var(--jade-700);
  --icon-warning-default: var(--saffron-400);
  --icon-warning-hover: var(--saffron-500);
  --jade-100: hsl(155 95% 93%);
  --jade-200: hsl(154 87% 85%);
  --jade-300: hsl(154 84% 73%);
  --jade-400: hsl(154 76% 58%);
  --jade-50: hsl(152 88% 97%);
  --jade-500: hsl(155 77% 45%);
  --jade-600: hsl(155 84% 36%);
  --jade-700: hsl(155 79% 29%);
  --jade-800: hsl(156 67% 20%);
  --jade-900: hsl(157 88% 10%);
  --jade-950: hsl(180 100% 6%);
  --saffron-100: hsl(53 90% 88%);
  --saffron-200: hsl(52 92% 77%);
  --saffron-300: hsl(49 90% 64%);
  --saffron-400: hsl(47 88% 53%);
  --saffron-50: hsl(54 83% 95%);
  --saffron-500: hsl(44 86% 47%);
  --saffron-600: hsl(40 88% 40%);
  --saffron-700: hsl(34 85% 33%);
  --saffron-800: hsl(27 66% 26%);
  --saffron-900: hsl(25 78% 14%);
  --saffron-950: hsl(25 80% 6%);
  --surface-error-default: var(--carnation-950);
  --surface-nautral-active: var(--gray-scale-900);
  --surface-nautral-default-1: var(--gray-scale-600);
  --surface-nautral-default-2: var(--gray-scale-700);
  --surface-nautral-default-3: var(--gray-scale-800);
  --surface-nautral-default-4: var(--gray-scale-900);
  --surface-nautral-disable: var(--gray-scale-100);
  --surface-nautral-modal-cover: hsl(0 0 9% / 0.6);
  --surface-nautral-transparent: hsl(0 0 24% / 0.3);
  --surface-primary-default: var(--azure-radiance-975);
  --surface-success-default: var(--jade-950);
  --surface-warning-default: var(--saffron-950);
  --text-error-default: var(--carnation-500);
  --text-error-default-2: var(--carnation-300);
  --text-nautral-black: var(--gray-scale-800);
  --text-nautral-default: var(--gray-scale-80);
  --text-nautral-disable: var(--gray-scale-400);
  --text-nautral-secondary: var(--gray-scale-100);
  --text-nautral-white: var(--gray-scale-80);
  --text-primary-default: var(--azure-radiance-600);
  --text-primary-hover: var(--azure-radiance-700);
  --text-success-default: var(--jade-600);
  --text-warning-default: var(--saffron-500);
}

:root {
  /** shadcn ui system colors */

  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@custom-variant dark (&:is(.dark *));

@layer base {
  * {
    @apply border-border-nautral-default outline-border-nautral-default/50;
  }
  body {
    @apply bg-background-nautral-body text-text-nautral-default;
  }
}

@theme inline {
  --color-*: initial;

  /** design system colors */
  --color-background-nautral-body: var(--background-nautral-body);
  --color-border-error-default: var(--border-error-default);
  --color-border-error-default-2: var(--border-error-default-2): ;
  --color-border-nautral-default: var(--border-nautral-default);
  --color-border-nautral-disable: var(--border-nautral-disable);
  --color-border-nautral-divider: var(--border-nautral-divider);
  --color-border-nautral-hover: var(--border-nautral-hover);
  --color-border-primary-default: var(--border-primary-default);
  --color-border-success-default: var(--border-success-default);
  --color-border-warning-default: var(--border-warning-default);
  --color-button-error-default: var(--button-error-default);
  --color-button-error-hover: var(--button-error-hover);
  --color-button-error-press: var(--button-error-press);
  --color-button-neutral-default: var(--button-neutral-default);
  --color-button-neutral-disable: var(--button-neutral-disable);
  --color-button-neutral-hover: var(--button-neutral-hover);
  --color-button-neutral-press: var(--button-neutral-press);
  --color-button-primary-default: var(--button-primary-default);
  --color-button-primary-hover: var(--button-primary-hover);
  --color-button-primary-press: var(--button-primary-press);
  --color-button-success-default: var(--button-success-default);
  --color-button-success-hover: var(--button-success-hover);
  --color-button-success-press: var(--button-success-press);
  --color-button-warning-default: var(--button-warning-default);
  --color-button-warning-hover: var(--button-warning-hover);
  --color-button-warning-press: var(--button-warning-press);
  --color-icon-error-default: var(--icon-error-default);
  --color-icon-error-default-2: var(--icon-error-default);
  --color-icon-error-hover: var(--icon-error-hover);
  --color-icon-neutral-black: var(--icon-neutral-black);
  --color-icon-neutral-default: var(--icon-neutral-default);
  --color-icon-neutral-disable: var(--icon-neutral-disable);
  --color-icon-neutral-hover: var(--icon-neutral-hover);
  --color-icon-neutral-secondary: var(--icon-neutral-secondary);
  --color-icon-neutral-white: var(--icon-neutral-white);
  --color-icon-primary-default: var(--icon-primary-default);
  --color-icon-primary-hover: var(--icon-primary-hover);
  --color-icon-success-default: var(--icon-success-default);
  --color-icon-success-hover: var(--icon-success-hover);
  --color-icon-warning-default: var(--icon-warning-default);
  --color-icon-warning-hover: var(--icon-warning-hover);
  --color-surface-error-default: var(--surface-error-default);
  --color-surface-nautral-active: var(--surface-nautral-active);
  --color-surface-nautral-default-1: var(--surface-nautral-default-1);
  --color-surface-nautral-default-2: var(--surface-nautral-default-2);
  --color-surface-nautral-default-3: var(--surface-nautral-default-3);
  --color-surface-nautral-default-4: var(--surface-nautral-default-4);
  --color-surface-nautral-disable: var(--surface-nautral-disable);
  --color-surface-nautral-modal-cover: #b2b2b280;
  --color-surface-nautral-transparent: #bababa4d;
  --color-surface-primary-default: var(--surface-primary-default);
  --color-surface-success-default: var(--surface-success-default);
  --color-surface-warning-default: var(--surface-warning-default);
  --color-text-error-default: var(--text-error-default);
  --color-text-error-default-2: var(--text-error-default-2);
  --color-text-nautral-black: var(--text-nautral-black);
  --color-text-nautral-default: var(--text-nautral-default);
  --color-text-nautral-disable: var(--text-nautral-disable);
  --color-text-nautral-secondary: var(--text-nautral-secondary);
  --color-text-nautral-white: var(--text-nautral-white);
  --color-text-primary-default: var(--text-primary-default);
  --color-text-primary-hover: var(--text-primary-hover);
  --color-text-success-default: var(--text-success-default);
  --color-text-warning-default: var(--text-warning-default);
  --shadow-carts: 0px 0px 20px 2px rgba(0, 0, 0, 0.25);

  /** shadcn colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}
