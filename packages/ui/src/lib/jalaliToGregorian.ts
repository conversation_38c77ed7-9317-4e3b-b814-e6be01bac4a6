function div(a: number, b: number) {
  // Integer division, discards the remainder (like floor division)
  return ~~(a / b);
}

interface jalaliToGregorian {
  (
    jy: number | string,
    jm: number | string,
    jd: number | string,
    h?: number,
    m?: number,
    s?: number,
  ): Date;
}

export const jalaliToGregorian: jalaliToGregorian = function (
  jy,
  jm,
  jd,
  h = 0,
  m = 0,
  s = 0,
) {
  // Convert input to integers
  jy = Number(jy);
  jm = Number(jm);
  jd = Number(jd);

  // Step 1: Set Gregorian base year and adjust Jalali year
  // If Jalali year > 979, use 1600 as the Gregorian base year and subtract 979 from Jalali year
  // Otherwise, use 621 as the Gregorian base year
  let gy;
  if (jy > 979) {
    gy = 1600;
    jy -= 979;
  } else {
    gy = 621;
  }

  // Step 2: Calculate the number of days since the base Jalali year
  // - 365*jy: total days for complete years
  // - div(jy, 33)*8: Jalali leap years follow a 33-year cycle with 8 leap years per cycle
  // - div((jy % 33 + 3), 4): extra leap years in the current cycle
  // - 78: offset to align with the Julian Day system
  // - jd: add the days of the current month
  // - (jm < 7 ? ...): months 1-6 have 31 days, months 7-12 have 30 days
  let days =
    365 * jy +
    div(jy, 33) * 8 +
    div((jy % 33) + 3, 4) +
    78 +
    jd +
    (jm < 7 ? (jm - 1) * 31 : (jm - 7) * 30 + 186);

  // Step 3: Convert days to Gregorian year
  // - 146097: days in 400 Gregorian years (400*365 + 100 - 4 + 1, accounting for leap years)
  gy += 400 * div(days, 146097);
  days %= 146097;

  // Step 4: Handle 100-year cycles (36524 days in 100 years)
  if (days > 36524) {
    gy += 100 * div(--days, 36524);
    days %= 36524;
    if (days >= 365) days++;
  }

  // Step 5: Handle 4-year cycles (1461 days in 4 years)
  gy += 4 * div(days, 1461);
  days %= 1461;

  // Step 6: Handle remaining years (365 days in a year)
  if (days > 365) {
    gy += div(days - 1, 365);
    days = (days - 1) % 365;
  }

  // Step 7: Now we have the Gregorian year and days into the year
  let gd = days + 1; // Gregorian day of the year

  // Step 8: Find Gregorian month and day
  // - sal_a: days in each Gregorian month (February has 29 days in leap years)
  const sal_a = [
    0,
    31,
    (gy % 4 === 0 && gy % 100 !== 0) || gy % 400 === 0 ? 29 : 28,
    31,
    30,
    31,
    30,
    31,
    31,
    30,
    31,
    30,
    31,
  ];
  let gm;
  for (gm = 0; gm < 13; gm++) {
    const v = sal_a[gm];
    if (typeof v === "undefined") break;
    if (gd <= v) break;
    gd -= v;
  }

  const gregorianDate = new Date(gy, gm - 1, gd, h, m, s);

  return gregorianDate;
};
