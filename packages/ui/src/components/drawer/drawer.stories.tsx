import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from "@workspace/ui/components/drawer";
import {
  DrawerProvider,
  useDrawer,
} from "@workspace/ui/components/drawer/use-drawer";
import { <PERSON>u, Settings } from "lucide-react";
import { useState } from "react";

const meta: Meta<typeof Drawer> = {
  title: "UI/Drawer",
  component: Drawer,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <DrawerProvider>
        <div className="container max-w-4xl p-8">
          <Story />
        </div>
      </DrawerProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Drawer>;

// Basic Drawer using shadcn components
export const BasicDrawer: Story = {
  render: () => (
    <Drawer>
      <DrawerTrigger asChild>
        <Button>Open Drawer</Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Drawer Title</DrawerTitle>
          <DrawerDescription>
            This is a basic drawer using shadcn components.
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4">
          <p>Drawer content goes here...</p>
        </div>
        <DrawerFooter>
          <DrawerClose asChild>
            <Button variant="outline">Close</Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  ),
};

// Different directions
export const DrawerDirections: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Drawer direction="bottom">
        <DrawerTrigger asChild>
          <Button>Bottom Drawer</Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Bottom Drawer</DrawerTitle>
            <DrawerDescription>
              This drawer slides from the bottom.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p>Content for bottom drawer...</p>
          </div>
        </DrawerContent>
      </Drawer>

      <Drawer direction="top">
        <DrawerTrigger asChild>
          <Button>Top Drawer</Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Top Drawer</DrawerTitle>
            <DrawerDescription>
              This drawer slides from the top.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p>Content for top drawer...</p>
          </div>
        </DrawerContent>
      </Drawer>

      <Drawer direction="left">
        <DrawerTrigger asChild>
          <Button>Left Drawer</Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Left Drawer</DrawerTitle>
            <DrawerDescription>
              This drawer slides from the left.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p>Content for left drawer...</p>
          </div>
        </DrawerContent>
      </Drawer>

      <Drawer direction="right">
        <DrawerTrigger asChild>
          <Button>Right Drawer</Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Right Drawer</DrawerTitle>
            <DrawerDescription>
              This drawer slides from the right.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p>Content for right drawer...</p>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  ),
};

// useDrawer Hook Example with shadcn components
function UseDrawerExample() {
  const { openDrawer, closeDrawer, closeAllDrawers, drawers } = useDrawer();

  const openShadcnDrawer = () => {
    openDrawer({
      direction: "bottom",
      content: (
        <div>
          <DrawerHeader>
            <DrawerTitle>Shadcn Drawer with useDrawer</DrawerTitle>
            <DrawerDescription>
              This drawer uses shadcn components with the useDrawer hook.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p className="text-muted-foreground mb-4 text-sm">
              You can use all the shadcn drawer components (DrawerHeader,
              DrawerTitle, DrawerDescription, DrawerFooter, etc.) with the
              useDrawer hook.
            </p>
            <div className="rounded-lg border p-4">
              <h4 className="mb-2 font-medium">Benefits:</h4>
              <ul className="space-y-1 text-sm">
                <li>• Full shadcn styling and behavior</li>
                <li>• Proper accessibility features</li>
                <li>• Consistent with your design system</li>
                <li>• Multiple drawer stacking support</li>
              </ul>
            </div>
          </div>
          <DrawerFooter>
            <Button onClick={() => closeDrawer("shadcn-drawer")}>Close</Button>
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </div>
      ),
      id: "shadcn-drawer",
    });
  };

  const openNestedDrawer = () => {
    openDrawer({
      direction: "bottom",
      content: (
        <div>
          <DrawerHeader>
            <DrawerTitle>Nested Shadcn Drawer</DrawerTitle>
            <DrawerDescription>
              This is a nested drawer using shadcn components. You can open
              multiple drawers on top of each other.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p className="text-muted-foreground mb-4 text-sm">
              Each drawer maintains its own state and can be closed
              independently. The z-index is automatically managed to ensure
              proper stacking.
            </p>
          </div>
          <DrawerFooter>
            <Button onClick={() => openAnotherDrawer()}>
              Open Another Drawer
            </Button>
            <Button variant="outline" onClick={() => closeDrawer("nested")}>
              Close This Drawer
            </Button>
          </DrawerFooter>
        </div>
      ),
      id: "nested",
    });
  };

  const openAnotherDrawer = () => {
    openDrawer({
      direction: "bottom",
      content: (
        <div>
          <DrawerHeader>
            <DrawerTitle>Third Shadcn Drawer</DrawerTitle>
            <DrawerDescription>
              This is the third drawer in the stack! Notice how it slides from
              the left.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p className="text-muted-foreground mb-4 text-sm">
              Each drawer can have a different direction and they all stack
              properly with automatic z-index management.
            </p>
            <div className="rounded-lg border p-4">
              <h4 className="mb-2 font-medium">Stack Order:</h4>
              <ol className="space-y-1 text-sm">
                <li>1. Right drawer (first)</li>
                <li>2. Bottom drawer (second)</li>
                <li>3. Left drawer (this one)</li>
              </ol>
            </div>
          </div>
          <DrawerFooter>
            <Button onClick={() => closeDrawer("another")}>
              Close This Drawer
            </Button>
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </div>
      ),
      id: "another",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button
          onClick={openShadcnDrawer}
          startAdornment={<Menu className="h-4 w-4" />}
        >
          Open Shadcn Drawer
        </Button>
        <Button
          onClick={openNestedDrawer}
          startAdornment={<Settings className="h-4 w-4" />}
        >
          Open Nested Drawer
        </Button>
        {drawers.length > 0 && (
          <Button variant="outline" onClick={closeAllDrawers}>
            Close All ({drawers.length})
          </Button>
        )}
      </div>

      {drawers.length > 0 && (
        <div className="text-muted-foreground text-sm">
          Active drawers: {drawers.map((d) => d.id).join(", ")}
        </div>
      )}
    </div>
  );
}

export const Hook: Story = {
  render: () => <UseDrawerExample />,
};

// Controlled Drawer Example
function ControlledDrawerExample() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">Controlled Drawer</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          This drawer is controlled by React state.
        </p>
      </div>

      <div className="flex justify-center">
        <Button onClick={() => setIsOpen(true)}>Open Controlled Drawer</Button>
      </div>

      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Controlled Drawer</DrawerTitle>
            <DrawerDescription>
              This drawer's open state is controlled by React state.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <p className="mb-4">
              The drawer is currently {isOpen ? "open" : "closed"}.
            </p>
            <p className="text-muted-foreground text-sm">
              You can control this drawer programmatically using the open and
              onOpenChange props.
            </p>
          </div>
          <DrawerFooter>
            <Button onClick={() => setIsOpen(false)}>Close Drawer</Button>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  );
}

export const ControlledDrawer: Story = {
  render: () => <ControlledDrawerExample />,
};
