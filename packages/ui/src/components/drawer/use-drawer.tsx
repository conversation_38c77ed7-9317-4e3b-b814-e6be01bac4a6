"use client";

import { DrawerContent } from "@workspace/ui/components/drawer";
import * as React from "react";
import { createPortal } from "react-dom";
import { Drawer as DrawerPrimitive } from "vaul";

export interface DrawerConfig {
  id: string;
  content: React.ReactNode;
  direction?: "top" | "bottom" | "left" | "right";
  onClose?: () => void;
  className?: string;
  overlayClassName?: string;
  open?: boolean;
  dismissible?: boolean;
}

interface DrawerState extends DrawerConfig {
  zIndex: number;
  isOpen: boolean;
  isClosing: boolean;
}

interface DrawerContextValue {
  drawers: DrawerState[];
  openDrawer: (config: Omit<DrawerConfig, "id"> & { id?: string }) => string;
  closeDrawer: (id: string) => void;
  closeAllDrawers: () => void;
  isDrawerOpen: (id: string) => boolean;
}

const DrawerContext = React.createContext<DrawerContextValue | null>(null);

const BASE_Z_INDEX = 50;
const Z_INDEX_INCREMENT = 10;

export function DrawerProvider({ children }: { children: React.ReactNode }) {
  const [drawers, setDrawers] = React.useState<DrawerState[]>([]);
  const [portalContainer, setPortalContainer] =
    React.useState<HTMLElement | null>(null);

  React.useEffect(() => {
    // Create or get the portal container
    let container = document.getElementById("drawer-portal-root");
    if (!container) {
      container = document.createElement("div");
      container.id = "drawer-portal-root";
      document.body.appendChild(container);
    }
    setPortalContainer(container);

    return () => {
      // Clean up empty portal container when component unmounts
      const existingContainer = document.getElementById("drawer-portal-root");
      if (
        existingContainer &&
        existingContainer.children.length === 0 &&
        existingContainer.parentNode
      ) {
        existingContainer.parentNode.removeChild(existingContainer);
      }
    };
  }, []);

  const openDrawer = React.useCallback(
    (config: Omit<DrawerConfig, "id"> & { id?: string }) => {
      const id =
        config.id ||
        `drawer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      setDrawers((prev) => {
        const zIndex = BASE_Z_INDEX + prev.length * Z_INDEX_INCREMENT;
        const newDrawer: DrawerState = {
          ...config,
          id,
          zIndex,
          isOpen: true,
          isClosing: false,
        };
        return [...prev, newDrawer];
      });
      return id;
    },
    [],
  );

  const closeDrawer = React.useCallback((id: string) => {
    // First, mark the drawer as closing to trigger the close animation
    setDrawers((prev) =>
      prev.map((drawer) =>
        drawer.id === id
          ? { ...drawer, isOpen: false, isClosing: true }
          : drawer,
      ),
    );

    // After animation duration, remove the drawer from DOM
    setTimeout(() => {
      setDrawers((prev) => prev.filter((drawer) => drawer.id !== id));
    }, 400); // Match this with the animation duration
  }, []);

  const closeAllDrawers = React.useCallback(() => {
    // First, mark all drawers as closing to trigger close animations
    setDrawers((prev) =>
      prev.map((drawer) => ({ ...drawer, isOpen: false, isClosing: true })),
    );

    // After animation duration, remove all drawers from DOM
    setTimeout(() => {
      setDrawers([]);
    }, 400); // Match this with the animation duration
  }, []);

  const isDrawerOpen = React.useCallback(
    (id: string) => {
      return drawers.some((drawer) => drawer.id === id && drawer.isOpen);
    },
    [drawers],
  );

  const contextValue: DrawerContextValue = {
    drawers,
    openDrawer,
    closeDrawer,
    closeAllDrawers,
    isDrawerOpen,
  };

  return (
    <DrawerContext.Provider value={contextValue}>
      {children}
      {portalContainer &&
        drawers.map((drawer) =>
          createPortal(
            <DrawerPortal
              key={drawer.id}
              drawer={drawer}
              onClose={() => closeDrawer(drawer.id)}
            />,
            portalContainer,
          ),
        )}
    </DrawerContext.Provider>
  );
}

interface DrawerPortalProps {
  drawer: DrawerState;
  onClose: () => void;
}

function DrawerPortal({ drawer, onClose }: DrawerPortalProps) {
  const handleOpenChange = (open: boolean) => {
    if (!open && !drawer.isClosing) {
      // Only trigger close if it's not already in closing state
      // This prevents double-closing when user clicks overlay or close button
      onClose();
      drawer.onClose?.();
    }
  };

  // Use the drawer's isOpen state for the vaul drawer
  // When isClosing is true, isOpen will be false, triggering the close animation
  return (
    <div style={{ zIndex: drawer.zIndex }}>
      <DrawerPrimitive.Root
        open={drawer.isOpen && !drawer.isClosing}
        onOpenChange={handleOpenChange}
        direction={drawer.direction || "bottom"}
        dismissible={drawer.dismissible ?? true}
      >
        <DrawerContent>{drawer.content}</DrawerContent>
      </DrawerPrimitive.Root>
    </div>
  );
}

export function useDrawer() {
  const context = React.useContext(DrawerContext);
  if (!context) {
    throw new Error("useDrawer must be used within a DrawerProvider");
  }
  return context;
}

// Helper type for creating drawer content with shadcn components
export interface ShadcnDrawerProps {
  children: React.ReactNode;
  className?: string;
}
