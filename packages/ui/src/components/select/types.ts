import { ReactNode } from "react";
import { Props, StylesConfig } from "react-select";

export interface ISelectItems<T> {
  value: T;
  label: string;
}

export type TSelectVariant = "select" | "dropdown";

export type ISelectFormItem = string | number | boolean;

export interface ISelectProps<T>
  extends Omit<Props<ISelectItems<T>>, "styles"> {
  variant?: TSelectVariant;
  title?: string;
  children?: ReactNode;
  errorMessage?: string;
  items: ISelectItems<T>[];
  styles?: StylesConfig<ISelectItems<any>, boolean>;
  hasInputWrapper?: boolean;
}

export type ISelectFormProps<T> = Omit<ISelectProps<T>, "onChange">;
