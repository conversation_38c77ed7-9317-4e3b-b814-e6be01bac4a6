import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import Select from "./Select";

const meta: Meta<typeof Select> = {
  component: Select,
  title: "UI/Select",
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Select>;

const items = [
  {
    label: "فروردین",
    value: 1,
  },
  {
    label: "اردیبهشت",
    value: 2,
  },
  {
    label: "خرداد",
    value: 3,
  },
  {
    label: "تیر",
    value: 4,
  },
  {
    label: "مرداد",
    value: 5,
  },
  {
    label: "شهریور",
    value: 6,
  },
];

export const Default: Story = {
  args: {
    items,
    defaultValue: {
      label: "فروردین",
      value: "value1",
    },
    variant: "select",
  },
};

export const DropDown: Story = {
  args: {
    items,
    defaultValue: {
      label: "فروردین",
      value: "value1",
    },
    variant: "dropdown",
  },
};
