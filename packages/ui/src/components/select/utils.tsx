import ArrowDown from "@workspace/ui/assets/icons/arrow-down.svg";
import { DropdownIndicatorProps } from "react-select";
import { twMerge } from "tailwind-merge";

import { ISelectItems } from "./types";

export function SelectIndicator<T>(
  props: DropdownIndicatorProps<ISelectItems<T>>,
) {
  const { isFocused } = props;

  return (
    <ArrowDown
      className={twMerge(
        isFocused ? "!text-primary" : "",
        "text-chevronLight hover:text-chevronHover focus:text-primary ml-3",
      )}
    />
  );
}

export function DropDownIndicator<T>(
  props: DropdownIndicatorProps<ISelectItems<T>>,
) {
  const { isFocused } = props;

  return (
    <ArrowDown
      className={twMerge(
        isFocused && "rotate-180",
        "text-dark_black01 h-2 w-2 transition-transform duration-500",
      )}
    />
  );
}
