import { StylesConfig } from "react-select";
import { ISelectItems } from "./types";

const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: (styles, state) => ({
    ...styles,
    width: "100%",
    minHeight: "32px",
    borderRadius: "8px",
    border:
      state.isFocused || state.menuIsOpen
        ? "1px solid var(--border-nautral-default)"
        : "1px solid var(--border-nautral-default)",
    boxShadow: "none",
    backgroundColor: "transparent",
    fontSize: "14px",
    cursor: "pointer",
    "&:hover": {
      border: "1px solid var(--border-nautral-default)",
    },
  }),
  menu: (styles) => ({
    ...styles,
    zIndex: 20,
    minWidth: "82px",
    fontSize: "10px",
    marginTop: "0",
    background: "var(--surface-nautral-default-1)",
    borderRadius: "4px",
    border: "1px solid var(--border-nautral-default)",
    boxShadow: "0px 4px 12px 0px rgba(23, 28, 35, 0.20)",
  }),
  menuList: (styles) => ({
    ...styles,
    padding: 0,
    // Hide scrollbar but keep scroll functionality
    scrollbarWidth: "none", // Firefox
    msOverflowStyle: "none", // Internet Explorer 10+
    "&::-webkit-scrollbar": {
      display: "none", // WebKit
    },
  }),
  container: (styles) => ({
    ...styles,
    // color: "var(--text-nautral-default) !important",
    // border: "1px solid var(--border-nautral-default)",
  }),
  input: (styles) => ({
    ...styles,
    color: "var(--text-nautral-default",
  }),
  placeholder: (styles) => ({
    ...styles,
    color: "var(--text-nautral-default",
  }),
  singleValue: (styles) => ({
    ...styles,
    color: "var(--text-nautral-default",
  }),
  option: (styles, state) => ({
    ...styles,
    fontWeight: 400,
    fontSize: "12px",
    textAlign: "right",
    padding: "8px 6px",
    color: state?.isSelected ? "var(--text-primary-default)" : "inherit",
    borderBottom: "1px solid var(--border-nautral-divider)",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      cursor: "pointer",
      background: "var(--surface-primary-default)",
    },
    "&:active": {
      background: "transparent",
      // color: "var(--text-primary-default)",
    },
    "&:focus": {
      background: "transparent",
    },
  }),
};
export default selectStyles;
