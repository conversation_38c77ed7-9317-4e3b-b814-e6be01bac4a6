/* eslint-disable no-nested-ternary */
import { StylesConfig } from "react-select";
import { ISelectItems } from "./types";

const selectStyles: StylesConfig<ISelectItems<any>, boolean> = {
  control: (styles) => ({
    ...styles,
    border: 0,
    width: "100%",
    display: "flex",
    minHeight: "32px",
    boxShadow: "none",
    cursor: "pointer",
    borderRadius: "8px",
    background: "transparent",
    justifyContent: "start !important",
  }),
  menu: (styles) => ({
    ...styles,
    zIndex: 20,
    marginTop: "0",
    padding: "14px 8px",
    borderRadius: "8px",
    background: "white",
    boxShadow: "0px 4px 12px 0px rgba(23, 28, 35, 0.20)",
  }),
  menuList: (styles) => ({
    ...styles,
    padding: 0,
    maxHeight: "170px",
    overflow: "auto",
    display: "flex",
    flexDirection: "column",
    gap: "10px",

    "::-webkit-scrollbar-thumb": {
      background: "#1477FF",
    },
  }),
  valueContainer: (styles) => ({
    ...styles,
    caretColor: "transparent",
    "& *": {
      fontSize: "16px",
      fontWeight: 700,
      color: "#4E5868",
    },
  }),
  container: (styles) => ({
    ...styles,
    "& *": {
      fontSize: "12px",
    },
  }),
  option: (styles, state) => ({
    ...styles,
    fontSize: "16px",
    fontWeight: 400,
    textAlign: "right",
    borderRadius: "8px !important",
    color: state?.isSelected ? "#1477FF" : "#646778",
    background: state?.isSelected ? "transparent" : "unset",
    "&:hover": {
      color: "#1477FF",
      cursor: "pointer",
    },
    "&:active": {
      background: "transparent",
    },
    "&:focus": {
      background: "transparent",
      color: "#1477FF",
    },
  }),
};

export default selectStyles;
