import { Ref, forwardRef } from "react";
import { default as ReactSelect, components } from "react-select";
import { twMerge } from "tailwind-merge";
import dropDownStyles from "./dropDownStyles";
import selectStyles from "./styles";
import { ISelectFormItem, ISelectProps } from "./types";
import { DropDownIndicator, SelectIndicator } from "./utils";

const Select = forwardRef(
  <T extends ISelectFormItem>(props: ISelectProps<T>, ref?: Ref<any>) => {
    const { items, children, variant = "select", ...restProps } = props;

    const styles = {
      select: selectStyles,
      dropdown: dropDownStyles,
    };

    const endAdornment = {
      select: SelectIndicator,
      dropdown: DropDownIndicator,
    };

    const Control = (propsC: any) => {
      const Co = components.Control;

      return children ? (
        <div className="relative h-full w-full !p-0">
          {children}
          <div className="absolute top-0 left-0 h-full w-full opacity-0">
            <Co {...propsC} />
          </div>
        </div>
      ) : (
        <Co {...propsC} />
      );
    };

    return (
      <ReactSelect
        {...restProps}
        options={items}
        ref={ref}
        className={twMerge("flex items-center", restProps?.className)}
        components={{
          DropdownIndicator: endAdornment[variant],
          IndicatorSeparator: () => null,
          Control,
        }}
        styles={restProps?.styles || styles[variant]}
      />
    );
  },
);

Select.displayName = "Select";

export default Select;
