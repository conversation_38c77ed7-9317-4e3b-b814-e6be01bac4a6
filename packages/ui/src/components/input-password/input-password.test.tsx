import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import * as React from "react";

import { InputPassword } from "../input-password/input-password";

describe("InputPassword Component", () => {
  // Basic rendering tests
  it("renders an input with type password by default", () => {
    render(<InputPassword placeholder="Enter password" />);

    // Password inputs don't have the "textbox" role, so we need to query by a different method
    const input = screen.getByDisplayValue(""); // Empty value initially
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("type", "password");
  });

  it("renders with a placeholder", () => {
    render(<InputPassword placeholder="Enter password" />);

    // Use a more specific query to avoid multiple matches
    const placeholderElement = screen.getByText("Enter password", {
      selector: "div.pointer-events-none",
    });
    expect(placeholderElement).toBeInTheDocument();
  });

  it("renders with an eye icon as end adornment", () => {
    render(<InputPassword placeholder="Enter password" />);

    const toggleButton = screen.getByRole("button", { name: "Show password" });
    expect(toggleButton).toBeInTheDocument();

    // Check that the eye icon is present (using aria-label since the icon itself doesn't have text)
    expect(toggleButton).toHaveAttribute("aria-label", "Show password");
  });

  // Password visibility toggle tests
  it("toggles password visibility when the eye icon is clicked", async () => {
    render(<InputPassword placeholder="Enter password" />);

    const input = screen.getByDisplayValue("");
    expect(input).toHaveAttribute("type", "password");

    const toggleButton = screen.getByRole("button", { name: "Show password" });
    await userEvent.click(toggleButton);

    // Password should now be visible
    expect(input).toHaveAttribute("type", "text");
    expect(toggleButton).toHaveAttribute("aria-label", "Hide password");

    // Click again to hide
    await userEvent.click(toggleButton);
    expect(input).toHaveAttribute("type", "password");
    expect(toggleButton).toHaveAttribute("aria-label", "Show password");
  });

  // Input value tests
  it("handles input correctly", async () => {
    render(<InputPassword placeholder="Enter password" />);

    const input = screen.getByDisplayValue("");
    await userEvent.type(input, "password123");

    expect(input).toHaveValue("password123");
  });

  it("renders with defaultValue", () => {
    render(
      <InputPassword placeholder="Enter password" defaultValue="password123" />,
    );

    const input = screen.getByDisplayValue("password123");
    expect(input).toHaveValue("password123");
  });

  // Controlled component tests
  it("works as a controlled component", () => {
    const handleChange = jest.fn();

    const TestComponent = () => {
      const [value, setValue] = React.useState("initial");

      const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
        handleChange(e);
      };

      return (
        <InputPassword
          placeholder="Enter password"
          value={value}
          onChange={onChange}
          data-testid="controlled-input"
        />
      );
    };

    render(<TestComponent />);

    const input = screen.getByTestId("controlled-input");
    expect(input).toHaveValue("initial");

    fireEvent.change(input, { target: { value: "newpassword" } });
    expect(input).toHaveValue("newpassword");
    expect(handleChange).toHaveBeenCalled();
  });

  // Disabled state tests
  it("renders in disabled state", () => {
    render(<InputPassword placeholder="Enter password" disabled />);

    const input = screen.getByDisplayValue("");
    expect(input).toBeDisabled();

    // The toggle button should still be present but might be visually styled as disabled
    const toggleButton = screen.getByRole("button", { name: "Show password" });
    expect(toggleButton).toBeInTheDocument();
  });

  // Prop passing tests
  it("passes through other props to the underlying Input component", () => {
    render(
      <InputPassword
        placeholder="Enter password"
        aria-label="Password field"
        helperText="Enter a secure password"
        data-testid="password-input"
      />,
    );

    const input = screen.getByTestId("password-input");
    expect(input).toHaveAttribute("aria-label", "Password field");
    expect(screen.getByText("Enter a secure password")).toBeInTheDocument();
  });

  // Accessibility tests
  it("has accessible toggle button", () => {
    render(<InputPassword placeholder="Enter password" />);

    const toggleButton = screen.getByRole("button", { name: "Show password" });
    expect(toggleButton).toHaveAttribute("aria-label", "Show password");
    expect(toggleButton).toHaveAttribute("type", "button");
  });
});
