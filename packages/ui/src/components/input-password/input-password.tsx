import { EyeIcon, EyeOffIcon } from "lucide-react";
import * as React from "react";

import { Input } from "@workspace/ui/components/input";

type InputPasswordProps = Omit<
  React.ComponentProps<typeof Input>,
  "type" | "endAdornment"
> & {
  className?: string;
};

const InputPassword = React.forwardRef<HTMLInputElement, InputPasswordProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    const togglePasswordVisibility = () => {
      setShowPassword((prev) => !prev);
    };

    const PasswordToggleIcon = (
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className="text-muted-foreground hover:text-foreground flex h-5 w-5 cursor-pointer items-center justify-center"
        aria-label={showPassword ? "Hide password" : "Show password"}
      >
        {showPassword ? (
          <EyeOffIcon className="h-4 w-4" />
        ) : (
          <EyeIcon className="h-4 w-4" />
        )}
      </button>
    );

    return (
      <Input
        ref={ref}
        type={showPassword ? "text" : "password"}
        className={className}
        endAdornment={PasswordToggleIcon}
        {...props}
      />
    );
  },
);

InputPassword.displayName = "InputPassword";

export { InputPassword };
