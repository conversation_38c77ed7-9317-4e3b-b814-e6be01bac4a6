import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { InputPassword } from "@workspace/ui/components/input-password";

const meta: Meta<typeof InputPassword> = {
  title: "UI/Input/InputPassword",
  component: InputPassword,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof InputPassword>;

export const Default: Story = {
  args: {
    placeholder: "Enter password",
  },
};

export const WithValue: Story = {
  args: {
    placeholder: "Enter password",
    defaultValue: "password123",
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Enter password",
    defaultValue: "password123",
    disabled: true,
  },
};
