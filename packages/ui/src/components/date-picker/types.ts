import { InputProps } from "@workspace/ui/components/input";
import { TDatePickerProps } from "headless-react-datepicker";
import { ReactNode } from "react";

export interface IDatePickerProps<T extends boolean>
  extends TDatePickerProps<T> {
  onCancel?: () => void;
  setIsOpen?: (val: boolean) => void;
  onConfirm?: (value: TDatePickerProps<T>["initialValue"]) => void;
  hasFooter?: boolean;
  isOpen?: boolean;
}

export interface IDatePickerInPopOverProps<T extends boolean>
  extends IDatePickerProps<T> {
  children?: ReactNode;
  className?: string;
  placement?: "bottom-start" | "bottom-end";
  closeOnConfirm?: boolean;
}

export interface IDatePickerInPopOverRefProps {
  close(): void;
  open(): void;
}

export interface IDatePickerInInputProps<T extends boolean = false>
  extends Omit<IDatePickerProps<T>, "setIsOpen">,
    Omit<InputProps, "value" | "onChange" | "onClick"> {
  placement?: "bottom-start" | "bottom-end";
  closeOnConfirm?: boolean;
  dateFormat?: string;
  dateLocale?: string;
}
