import Calendar from "@workspace/ui/assets/icons/calendar.svg";
import CloseIcon from "@workspace/ui/assets/icons/close.svg";

interface ITitleProps {
  onClose: () => void;
}

export function Title({ onClose }: ITitleProps) {
  return (
    <div className="border-b-border-nautral-divider mx-3 flex items-center justify-between border-b pt-[11px] pb-[9px]">
      <div className="flex items-center gap-1">
        <Calendar className="text-text-nautral-default size-[18px]" />
        <span className="text-xs">انتخاب تاریخ</span>
      </div>
      <CloseIcon onClick={onClose} className="size-2.5 cursor-pointer" />
    </div>
  );
}
