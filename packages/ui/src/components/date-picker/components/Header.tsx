import ArrowLeft from "@workspace/ui/assets/icons/arrow-left.svg";
import ArrowRight from "@workspace/ui/assets/icons/arrow-right.svg";
import { Select } from "@workspace/ui/components/select";
import { useDatePickerContext } from "headless-react-datepicker";

export function Header() {
  const {
    yearInTheCalendar,
    monthInTheCalendar,
    yearsList,
    goToMonth,
    goToYear,
    monthsList,
    goToNextMonth,
    goToPrevMonth,
  } = useDatePickerContext();

  return (
    <div className="mx-2 mt-2 mb-5 flex items-center justify-between">
      <div
        className="flex items-center select-none"
        onClick={goToPrevMonth}
        data-testid="c6bfe9a7-aa3d-45d4-a6ec-9e39364832ce"
      >
        <ArrowRight className="size-3" />
        <span className="text-text-nautral-default cursor-pointer text-[10px]">
          ماه قبلی
        </span>
      </div>
      <div className="text-text-nautral-default flex items-center gap-1 text-sm">
        {monthInTheCalendar && (
          <>
            <Select
              items={monthsList || []}
              onChange={(val: any) => goToMonth(val.value)}
            >
              <span
                className="text-text-nautral-default !text-sm font-bold"
                data-test="8a92735c-212c-49e0-908f-a91c76633102"
              >
                {monthsList?.[monthInTheCalendar - 1]?.label || "-"}
              </span>
            </Select>

            <Select
              className="mt-0.5 block h-auto w-full"
              onChange={(val: any) => goToYear(val.value)}
              items={
                yearsList?.map((item) => ({
                  label: item.toString(),
                  value: item,
                })) || []
              }
            >
              <span
                className="text-text-nautral-default !text-sm font-bold"
                data-test="8db386c7-8202-4c0f-a7d5-ba372c81888a"
              >
                {yearInTheCalendar}
              </span>
            </Select>
          </>
        )}
      </div>
      <div
        className="flex cursor-pointer items-center select-none"
        onClick={goToNextMonth}
        data-testid="date-picker-title-856a9db8-8f73-4cc7-923b-5867f8b6c98e"
      >
        <span className="text-text-nautral-default text-[10px]">ماه بعدی</span>
        <ArrowLeft className="size-3" />
      </div>{" "}
    </div>
  );
}
