import { Button } from "@workspace/ui/components/button";

export interface IFooterProps {
  disabled: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export function Footer({ disabled, onCancel, onConfirm }: IFooterProps) {
  return (
    <div className="mx-3 my-3 flex items-center justify-between gap-2">
      <Button
        size="sm"
        className="flex-1"
        variant="outline"
        onClick={onCancel}
        data-test="4e6652e8-2c90-45ee-b76b-e80c32a941df"
      >
        پاک کردن
      </Button>
      <Button
        size="sm"
        disabled={disabled}
        className="flex-1"
        variant="fill"
        onClick={onConfirm}
        data-test="944abd5c-1e16-406a-8ea1-fe926a908e0d"
      >
        تایید
      </Button>
    </div>
  );
}
