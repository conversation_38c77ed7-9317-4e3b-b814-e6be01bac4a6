import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { TDatePickerProps } from "headless-react-datepicker";
import { Ref, forwardRef, useImperativeHandle, useState } from "react";
import DatePicker from "./DatePicker";
import {
  IDatePickerInPopOverProps,
  IDatePickerInPopOverRefProps,
} from "./types";

function DatePickerInPopOver<T extends boolean = boolean>(
  {
    children,
    placement = "bottom-start",
    className = "",
    onCancel,
    onConfirm,
    closeOnConfirm = true,
    ...restProps
  }: IDatePickerInPopOverProps<T>,
  ref: Ref<IDatePickerInPopOverRefProps>, // Ref should be of type DatePickerInPopOverRef
) {
  const [isOpen, setIsOpen] = useState(restProps?.isOpen ?? false);

  const toggleOpen = (v: any) => setIsOpen(v);

  const handleCancel = () => {
    onCancel?.();
    setIsOpen(false);
  };

  const handleConfirm = (value?: TDatePickerProps<T>["initialValue"]) => {
    onConfirm?.(value);

    if (closeOnConfirm) {
      setIsOpen(false);
    }
  };

  useImperativeHandle(ref, () => ({
    close() {
      setIsOpen(false);
    },
    open() {
      setIsOpen(true);
    },
  }));

  return (
    <Popover open={isOpen} onOpenChange={(o) => toggleOpen(o)}>
      <PopoverTrigger asChild>{children}</PopoverTrigger>
      <PopoverContent
        className="bg-surface-nautral-default-2 border-border-nautral-default shadow-carts overflow-hidden rounded-lg border p-0"
        dir="rtl"
      >
        <DatePicker
          setIsOpen={toggleOpen}
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          {...restProps}
        />
      </PopoverContent>
    </Popover>
  );
}

const ForwardedDatePickerInPopOver = forwardRef(DatePickerInPopOver) as <
  T extends boolean = boolean,
>(
  props: IDatePickerInPopOverProps<T> & {
    ref?: Ref<IDatePickerInPopOverRefProps>;
  },
) => React.ReactElement;

export default ForwardedDatePickerInPopOver;
