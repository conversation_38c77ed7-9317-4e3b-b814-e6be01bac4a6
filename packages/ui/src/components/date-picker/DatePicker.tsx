import { Footer } from "@workspace/ui/components/date-picker/components/Footer";
import { Title } from "@workspace/ui/components/date-picker/components/Title";
import DatePickerProvider, {
  DaySlots,
  TDatePickerProps,
  WeekDays,
} from "headless-react-datepicker";
import "headless-react-datepicker/dist/styles.css";
import { useCallback, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Header } from "./components/Header";
import "./DatePicker.css";
import { IDatePickerProps } from "./types";

export default function DatePicker<T extends boolean>({
  onCancel,
  setIsOpen,
  onConfirm,
  hasFooter = true,
  ...restProps
}: IDatePickerProps<T>) {
  const [value, setValue] = useState<TDatePickerProps<T>["initialValue"]>();

  const onChange = useCallback(
    (date: any) => {
      setValue(date);
      restProps?.onChange?.(date);

      // For non-range pickers, confirm immediately
      if (!restProps?.isRange) {
        onConfirm?.(date);
      }
      // For range pickers, confirm and close when both dates are selected
      else if (
        restProps?.isRange &&
        Array.isArray(date) &&
        date.length === 2 &&
        date[0] &&
        date[1]
      ) {
        onConfirm?.(date as TDatePickerProps<T>["initialValue"]);
      }
    },
    [onConfirm, restProps, hasFooter],
  );

  const isRangeButSingleSelected =
    restProps?.isRange && Array.isArray(value) && value?.length === 1;

  return (
    <div
      style={{
        width: "288px",
        margin: "0 auto",
        minHeight: "310px",
      }}
    >
      <DatePickerProvider<T>
        {...restProps}
        config={{
          locale: "fa-IR",
          weekends: ["friday"],
          ...restProps?.config,
        }}
        initialValue={restProps?.initialValue || value}
        calendar="persian"
        onChange={onChange}
      >
        <Title
          onClose={() => {
            setIsOpen?.(false);
            onCancel?.();
          }}
        />

        <Header />

        <div className="w-full px-2">
          <WeekDays
            className="!border-b-border-nautral-divider text-text-nautral-default border-b-[0.5px] pb-1.5 text-sm font-normal"
            style={{ textAlign: "center" }}
          />
        </div>

        <DaySlots
          slotClassName={twMerge(
            "dayslot text-text-nautral-default w-[28px] cursor-default max-h-[28px] flex items-center justify-center ",
          )}
          slotParentClassName="py-0.5 mb-1 flex justify-center !cursor-default"
          selectableClassName="hover:!bg-button-primary-hover rounded-[4px]"
          weekendClassName="text-text-error-default"
          inSelectedRangeParentClassName={twMerge(
            isRangeButSingleSelected
              ? "bg-transparent"
              : "!bg-surface-nautral-default-1",
          )}
          inHoveredRangeParentClassName="bg-surface-nautral-default-1"
          endOfRangeClassName=" bg-button-primary-default !rounded-tl-[4px] rounded-bl-[4px] !px-0 text-text-nautral-white font-bold"
          endOfRangeParentClassName=" w-[35px] justify-end pr-0 pl-0.5"
          startOfRangeParentClassName={
            isRangeButSingleSelected
              ? ""
              : "w-[35px] justify-start pl-0 pr-0.5 mr-auto"
          }
          startOfRangeClassName={twMerge(
            " bg-button-primary-default text-text-nautral-white font-bold",
            isRangeButSingleSelected
              ? "border !border-2 border-surface-nautral-default-1   "
              : "!rounded-tr-[4px] rounded-br-[4px] ",
          )}
          disableClassName="opacity-50"
          todayClassName="border border-border-nautral-default rounded-[4px]"
          parentClassName=" text-sm py-[1px] date-picker-container"
          selectedClassName="bg-button-primary-default rounded-[4px] hover:!bg-azureRadiance800 "
        />
        {hasFooter && (
          <Footer
            disabled={Array.isArray(value) ? !value?.[1] : !value}
            onCancel={() => {
              onCancel?.();
              setValue(undefined);
            }}
            onConfirm={() => onConfirm?.(value)}
          />
        )}
      </DatePickerProvider>
    </div>
  );
}
