import { Input } from "@workspace/ui/components/input";
import { dateConverter } from "@workspace/ui/lib/dateConverter";
import { cn } from "@workspace/ui/lib/utils";
import { TDatePickerProps } from "headless-react-datepicker";
import { Ref, forwardRef, useState } from "react";
import DatePickerInPopOver from "./DatePickerInPopOver";
import { IDatePickerInInputProps, IDatePickerInPopOverRefProps } from "./types";

function DatePickerInInput<T extends boolean = false>(
  {
    placement = "bottom-start",
    closeOnConfirm = true,
    dateFormat = "YYYY/MM/DD",
    dateLocale = "fa-IR",
    startAdornment,
    endAdornment,
    title,
    placeholder,
    className,
    helperText,
    variant,
    size,
    disabled,
    readOnly,
    onCancel,
    onConfirm,
    onChange,
    initialValue,
    ...restProps
  }: IDatePickerInInputProps<T>,
  ref: Ref<IDatePickerInPopOverRefProps>,
) {
  const [value, setValue] =
    useState<TDatePickerProps<T>["initialValue"]>(initialValue);

  const handleConfirm = (
    selectedValue?: TDatePickerProps<T>["initialValue"],
  ) => {
    setValue(selectedValue);
    onConfirm?.(selectedValue);
  };

  const handleCancel = () => {
    onCancel?.();
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleChange = (selectedValue: any) => {
    setValue(selectedValue);

    onChange?.(selectedValue);
  };

  const formatDateValue = (dateValue: TDatePickerProps<T>["initialValue"]) => {
    if (!dateValue) return "";

    if (Array.isArray(dateValue)) {
      // Handle range dates
      const formattedDates = dateValue.filter(Boolean).map((date) =>
        dateConverter(date as Date)
          .locale(dateLocale)
          .calendar("persian")
          .format(dateFormat),
      );
      return formattedDates.join(" - ");
    } else {
      // Handle single date
      return dateConverter(dateValue as Date)
        .locale(dateLocale)
        .calendar("persian")
        .format(dateFormat);
    }
  };

  const displayValue = formatDateValue(value);

  return (
    <DatePickerInPopOver
      ref={ref}
      placement={placement}
      closeOnConfirm={closeOnConfirm}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      onChange={handleChange}
      initialValue={value}
      hasFooter={false}
      {...restProps}
    >
      <Input
        title={title}
        placeholder={placeholder}
        value={displayValue}
        startAdornment={startAdornment}
        endAdornment={endAdornment}
        className={cn(className, "text-start")}
        helperText={helperText}
        variant={variant}
        size={size}
        disabled={disabled}
        readOnly={readOnly || true} // Always readonly since user should use picker
        onClick={() => {
          // Input click will trigger popover through DatePickerInPopOver
        }}
      />
    </DatePickerInPopOver>
  );
}

const ForwardedDatePickerInInput = forwardRef(DatePickerInInput) as <
  T extends boolean = false,
>(
  props: IDatePickerInInputProps<T> & {
    ref?: Ref<IDatePickerInPopOverRefProps>;
  },
) => React.ReactElement;

export default ForwardedDatePickerInInput;
