"use client";

import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";
import { cva } from "class-variance-authority";

const checkboxRootClassName = cva(
  "peer outline-border-primary-default data-[state=checked]:text-primary-foreground focus-visible:border-ring focus-visible:ring-ring/50 size-4 shrink-0 cursor-pointer border-transparent shadow-xs outline-[1.5px] transition-shadow focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      size: {
        xl: "size-6 border-[2px] rounded-sm",
        lg: "size-5 border-[2px] rounded-sm",
        md: "size-[18px] border-[2px] rounded-sm",
        sm: "size-4 border-[1.5px] rounded-xs",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const checkboxIndicatorClassName = cva(
  "data-[state=checked]:bg-icon-primary-default h-full w-full flex items-center justify-center text-current transition-none",
  {
    variants: {
      size: {
        xl: "rounded-xs",
        lg: "rounded-xs",
        md: "rounded-xs",
        sm: "rounded-sm",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

type CheckboxRadixUiProps = React.ComponentProps<
  typeof CheckboxPrimitive.Root
> & {
  size?: "xl" | "lg" | "md" | "sm";
};

function CheckboxRadixUi({ className, size, ...props }: CheckboxRadixUiProps) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(checkboxRootClassName({ size, className }), "rounded-sm")}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className={cn(checkboxIndicatorClassName({ size, className }))}
      >
        <CheckIcon className="size-3.5" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

type CheckboxWithLabelProps = React.ComponentProps<typeof CheckboxRadixUi> & {
  label?: string;
  size?: "xl" | "lg" | "md" | "sm";
};

const checkboxLabelClassName = cva(
  "cursor-pointer text-text-nautral-default leading-none font-normal peer-disabled:cursor-not-allowed peer-disabled:text-text-nautral-disable",
  {
    variants: {
      size: {
        xl: "text-xs",
        lg: "text-xs",
        md: "text-xs",
        sm: "text-[10px]",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

function CheckboxWithLabel({ label, size, ...props }: CheckboxWithLabelProps) {
  return (
    <div className="flex cursor-pointer items-center space-x-2">
      <CheckboxRadixUi {...props} size={size} />
      {label && (
        <label
          htmlFor={props.id}
          className={cn(checkboxLabelClassName({ size }), "")}
        >
          {label}
        </label>
      )}
    </div>
  );
}

export { CheckboxWithLabel as Checkbox };
