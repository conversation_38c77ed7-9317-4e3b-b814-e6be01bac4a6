"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  DrawerClose,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@workspace/ui/components/drawer";
import { useResponsiveModal } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { cn } from "@workspace/ui/lib/utils";
import {
  AlertTriangle,
  CheckCircle,
  Info,
  Loader,
  XCircle,
} from "lucide-react";
import * as React from "react";

export type ConfirmVariant = "warning" | "success" | "danger" | "info";

export interface ConfirmConfig {
  title: string;
  description: string;
  body?: React.ReactNode;
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
  variant: ConfirmVariant;
  confirmText?: string | null;
  cancelText?: string | null;
}

const variantConfig = {
  warning: {
    icon: AlertTriangle,
    iconClassName: "text-text-warning-default",
    titleClassName: "text-text-warning-default",
    containerClassName:
      "border-border-warning-default/20 bg-surface-warning-default/5",
    textClassName: "text-text-warning-default/80",
    confirmButtonColor: "warning" as const,
  },
  success: {
    icon: CheckCircle,
    iconClassName: "text-text-success-default",
    titleClassName: "text-text-success-default",
    containerClassName:
      "border-border-success-default/20 bg-surface-success-default/5",
    textClassName: "text-text-success-default/80",
    confirmButtonColor: "success" as const,
  },
  danger: {
    icon: XCircle,
    iconClassName: "text-text-error-default",
    titleClassName: "text-text-error-default",
    containerClassName:
      "border-border-error-default/20 bg-surface-error-default/5",
    textClassName: "text-text-error-default/80",
    confirmButtonColor: "destructive" as const,
  },
  info: {
    icon: Info,
    iconClassName: "text-text-primary-default",
    titleClassName: "text-text-primary-default",
    containerClassName:
      "border-border-primary-default/20 bg-surface-primary-default/5",
    textClassName: "text-text-primary-default/80",
    confirmButtonColor: "default" as const,
  },
} as const;

export function useConfirm() {
  const { openModal, closeModal, isMobile } = useResponsiveModal();

  const confirm = React.useCallback(
    (config: ConfirmConfig) => {
      const {
        title,
        description,
        body,
        onConfirm,
        onCancel,
        variant,
        confirmText = "Confirm",
        cancelText = "Cancel",
      } = config;

      const variantStyles = variantConfig[variant];
      const Icon = variantStyles.icon;

      const modalId = `confirm-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Create a state container for the modal content
      const ConfirmModalContent = () => {
        const [isLoading, setIsLoading] = React.useState(false);

        const handleConfirm = async () => {
          try {
            setIsLoading(true);
            const result = onConfirm();

            // Check if result is a promise
            if (result && typeof result.then === "function") {
              await result;
            }

            closeModal(modalId);
          } catch (error) {
            console.error("Confirm action failed:", error);
          } finally {
            setIsLoading(false);
          }
        };

        const handleCancel = () => {
          if (isLoading) return; // Prevent cancel during loading
          closeModal(modalId);
          onCancel?.();
        };

        return isMobile ? (
          // Mobile: Use Drawer components
          <div>
            <DrawerHeader>
              <DrawerTitle
                className={cn(
                  "flex items-center gap-2",
                  variantStyles.titleClassName,
                )}
              >
                <Icon className="h-5 w-5" />
                {title}
              </DrawerTitle>
              <DrawerDescription>{description}</DrawerDescription>
            </DrawerHeader>
            {body}
            <DrawerFooter>
              {confirmText && (
                <Button
                  onClick={handleConfirm}
                  color={variantStyles.confirmButtonColor}
                  disabled={isLoading}
                  startAdornment={
                    isLoading ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : undefined
                  }
                >
                  {confirmText}
                </Button>
              )}
              {cancelText && (
                <DrawerClose asChild>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    {cancelText}
                  </Button>
                </DrawerClose>
              )}
            </DrawerFooter>
          </div>
        ) : (
          // Desktop: Use Dialog components
          <div>
            <DialogHeader>
              <DialogTitle
                className={cn(
                  "flex items-center gap-2",
                  variantStyles.titleClassName,
                )}
              >
                <Icon className="h-5 w-5" />
                {title}
              </DialogTitle>
              <DialogDescription>{description}</DialogDescription>
            </DialogHeader>

            {body}

            <DialogFooter className="mt-4">
              {cancelText && (
                <DialogClose asChild>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    {cancelText}
                  </Button>
                </DialogClose>
              )}
              {confirmText && (
                <Button
                  onClick={handleConfirm}
                  color={variantStyles.confirmButtonColor}
                  disabled={isLoading}
                  startAdornment={
                    isLoading ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : undefined
                  }
                >
                  {confirmText}
                </Button>
              )}
            </DialogFooter>
          </div>
        );
      };

      return openModal({
        id: modalId,
        content: <ConfirmModalContent />,
        onClose: () => onCancel?.(),
      });
    },
    [openModal, closeModal, isMobile],
  );

  return { confirm };
}
