import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { DialogProvider } from "@workspace/ui/components/dialog/use-dialog";
import { DrawerProvider } from "@workspace/ui/components/drawer/use-drawer";
import { ResponsiveModalProvider } from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import {
  AlertTriangle,
  CheckCircle,
  Info,
  Trash2,
  XCircle,
} from "lucide-react";
import { useConfirm, type ConfirmVariant } from "./use-confirm";

const meta: Meta = {
  title: "UI/useConfirm",
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A confirm hook that uses useResponsiveModal to show confirmation dialogs with different variants (warning, success, danger, info). Automatically switches between drawer (mobile) and dialog (desktop).",
      },
    },
  },
  decorators: [
    (Story) => (
      <DialogProvider>
        <DrawerProvider>
          <ResponsiveModalProvider>
            <div className="container max-w-4xl p-8">
              <Story />
            </div>
          </ResponsiveModalProvider>
        </DrawerProvider>
      </DialogProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj;

// Basic Confirm Examples
function BasicConfirmExample() {
  const { confirm } = useConfirm();

  const handleWarningConfirm = () => {
    confirm({
      title: "Delete Item",
      description:
        "Are you sure you want to delete this item? This action cannot be undone.",
      variant: "warning",
      onConfirm: async () => {
        // Simulate async operation with loading state
        await new Promise((resolve) => setTimeout(resolve, 2000));
        alert("Item deleted!");
      },
      onCancel: () => {
        console.log("Delete cancelled");
      },
    });
  };

  const handleSuccessConfirm = () => {
    confirm({
      title: "Save Changes",
      description: "Your changes will be saved and applied immediately.",
      variant: "success",
      confirmText: "Save",
      cancelText: "Discard",
      onConfirm: () => {
        alert("Changes saved!");
      },
      onCancel: () => {
        console.log("Changes discarded");
      },
    });
  };

  const handleDangerConfirm = () => {
    confirm({
      title: "Permanent Deletion",
      description:
        "This will permanently delete all data and cannot be recovered.",
      variant: "danger",
      confirmText: "Delete Forever",
      onConfirm: () => {
        alert("Data permanently deleted!");
      },
    });
  };

  const handleInfoConfirm = () => {
    confirm({
      title: "Update Available",
      description: "A new version is available. Would you like to update now?",
      variant: "info",
      confirmText: "Update Now",
      cancelText: "Later",
      onConfirm: () => {
        alert("Starting update...");
      },
    });
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">Confirm Hook Examples</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          Click the buttons below to see different confirmation variants.
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Button
          onClick={handleWarningConfirm}
          startAdornment={<AlertTriangle className="h-4 w-4" />}
          color="warning"
        >
          Warning Confirm
        </Button>
        <Button
          onClick={handleSuccessConfirm}
          startAdornment={<CheckCircle className="h-4 w-4" />}
          color="success"
        >
          Success Confirm
        </Button>
        <Button
          onClick={handleDangerConfirm}
          startAdornment={<XCircle className="h-4 w-4" />}
          color="destructive"
        >
          Danger Confirm
        </Button>
        <Button
          onClick={handleInfoConfirm}
          startAdornment={<Info className="h-4 w-4" />}
          color="default"
        >
          Info Confirm
        </Button>
      </div>
    </div>
  );
}

export const BasicConfirm: Story = {
  render: () => <BasicConfirmExample />,
};

// Complex Workflow Example
function WorkflowExample() {
  const { confirm } = useConfirm();

  const handleComplexWorkflow = () => {
    confirm({
      title: "Delete User Account",
      description:
        "This will permanently delete the user account and all associated data.",
      variant: "danger",
      confirmText: "Delete Account",
      onConfirm: async () => {
        // Simulate async deletion with loading state
        await new Promise((resolve) => setTimeout(resolve, 3000));

        // Show success confirmation after deletion
        setTimeout(() => {
          confirm({
            title: "Account Deleted",
            description: "The user account has been successfully deleted.",
            variant: "success",
            confirmText: "Ok",
            cancelText: null,
            onConfirm: () => {
              console.log("Workflow completed!");
            },
          });
        }, 400);
      },
    });
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">Complex Workflow</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          This example shows how to chain multiple confirmations together.
        </p>
      </div>

      <div className="flex justify-center">
        <Button
          onClick={handleComplexWorkflow}
          startAdornment={<Trash2 className="h-4 w-4" />}
          color="destructive"
        >
          Delete User Account
        </Button>
      </div>
    </div>
  );
}

export const ComplexWorkflow: Story = {
  render: () => <WorkflowExample />,
};

// All Variants Showcase
function AllVariantsExample() {
  const { confirm } = useConfirm();

  const variants: Array<{
    variant: ConfirmVariant;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
  }> = [
    {
      variant: "warning",
      title: "Warning Confirmation",
      description: "This is a warning confirmation dialog.",
      icon: AlertTriangle,
    },
    {
      variant: "success",
      title: "Success Confirmation",
      description: "This is a success confirmation dialog.",
      icon: CheckCircle,
    },
    {
      variant: "danger",
      title: "Danger Confirmation",
      description: "This is a danger confirmation dialog.",
      icon: XCircle,
    },
    {
      variant: "info",
      title: "Info Confirmation",
      description: "This is an info confirmation dialog.",
      icon: Info,
    },
  ];

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">All Variants</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          Showcase of all available confirmation variants.
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        {variants.map(({ variant, title, description, icon: Icon }) => (
          <Button
            key={variant}
            onClick={() =>
              confirm({
                title,
                description,
                variant,
                onConfirm: () => alert(`${variant} confirmed!`),
              })
            }
            startAdornment={<Icon className="h-4 w-4" />}
            color={
              variant === "warning"
                ? "warning"
                : variant === "success"
                  ? "success"
                  : variant === "danger"
                    ? "destructive"
                    : "default"
            }
            className="capitalize"
          >
            {variant}
          </Button>
        ))}
      </div>
    </div>
  );
}

export const AllVariants: Story = {
  render: () => <AllVariantsExample />,
};
