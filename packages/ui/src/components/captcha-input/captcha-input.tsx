import React, { useState, useEffect, useCallback } from "react";
import {
  GoogleReCaptchaProvider,
  useGoogleReCaptcha,
} from "react-google-recaptcha-v3";
import { RefreshCw, Shield } from "lucide-react";

// Types
export const CaptchaTypeEnum = {
  None: 0,
  GoogleRecaptchaV3: 100,
  Text: 200,
} as const;

export type CaptchaTypeEnum =
  (typeof CaptchaTypeEnum)[keyof typeof CaptchaTypeEnum];

export interface ApiResultOfCaptchaTypeEnum {
  data?: CaptchaTypeEnum;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}

export interface RequestCaptchaOutputDTO {
  captchaImage?: string;
  captchaSecretKey?: string;
}

export type ApiResultOfRequestCaptchaOutputDTOData =
  RequestCaptchaOutputDTO | null;

export interface ApiResultOfRequestCaptchaOutputDTO {
  data?: ApiResultOfRequestCaptchaOutputDTOData;
  isSuccess?: boolean;
  errorCode?: string | null;
  errorMessage?: string | null;
}

// Hook types
interface UseCaptchaWhichCaptchaIsActiveReturn {
  data?: ApiResultOfCaptchaTypeEnum;
  isLoading?: boolean;
  error?: any;
}

interface UseCaptchaRequestTextCaptchaReturn {
  data?: ApiResultOfRequestCaptchaOutputDTO;
  isLoading?: boolean;
  error?: any;
  refetch?: () => void;
}

// Props interface
interface CaptchaInputProps {
  useCaptchaWhichCaptchaIsActive: () => UseCaptchaWhichCaptchaIsActiveReturn;
  useCaptchaRequestTextCaptcha: () => UseCaptchaRequestTextCaptchaReturn;
  onCaptchaChange: (captchaData: {
    token?: string;
    secretKey?: string;
    userInput?: string;
  }) => void;
  googleRecaptchaSiteKey?: string;
  className?: string;
  error?: string;
}

// Google reCAPTCHA component
export const GoogleCaptchaComponent: React.FC<{
  onCaptchaChange: (token: string) => void;
}> = ({ onCaptchaChange }) => {
  const { executeRecaptcha } = useGoogleReCaptcha();

  const handleCaptchaSubmission = useCallback(async () => {
    if (!executeRecaptcha) {
      console.log("Execute recaptcha not available yet");
      return;
    }

    try {
      const token = await executeRecaptcha("login");
      console.log("ttttttttttttttttt", token);

      onCaptchaChange(token);
    } catch (error) {
      console.error("reCAPTCHA execution failed:", error);
    }
  }, [executeRecaptcha, onCaptchaChange]);

  useEffect(() => {
    handleCaptchaSubmission();
  }, [handleCaptchaSubmission]);

  return (
    <div className="flex items-center gap-2 rounded-lg border bg-gray-50 p-3">
      <Shield className="h-5 w-5 text-green-600" />
      <span className="text-sm text-gray-700">Protected by reCAPTCHA</span>
    </div>
  );
};

// Image CAPTCHA component
const ImageCaptchaComponent: React.FC<{
  captchaData?: ApiResultOfRequestCaptchaOutputDTO;
  onRefresh: () => void;
  onInputChange: (value: string) => void;
  isLoading?: boolean;
  error?: string;
}> = ({ captchaData, onRefresh, onInputChange, isLoading, error }) => {
  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    onInputChange(value);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-3">
        <div className="relative">
          {captchaData?.data?.captchaImage ? (
            <img
              src={captchaData.data.captchaImage}
              alt="CAPTCHA"
              className="h-16 rounded-lg border bg-white p-2"
              style={{ minWidth: "120px" }}
            />
          ) : (
            <div
              className="flex h-16 items-center justify-center rounded-lg border bg-gray-100 p-2"
              style={{ minWidth: "120px" }}
            >
              {isLoading ? (
                <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
              ) : (
                <span className="text-sm text-gray-400">No image</span>
              )}
            </div>
          )}
        </div>
        <button
          type="button"
          onClick={onRefresh}
          disabled={isLoading}
          className="rounded-lg border border-gray-300 p-2 transition-colors hover:bg-gray-50 disabled:opacity-50"
          title="Refresh CAPTCHA"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
        </button>
      </div>

      <div>
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          placeholder="Enter the text shown in the image"
          className={`w-full rounded-lg border px-3 py-2 focus:border-transparent focus:ring-2 focus:ring-blue-500 focus:outline-none ${
            error ? "border-red-500" : "border-gray-300"
          }`}
        />
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
      </div>
    </div>
  );
};

// Main CAPTCHA Input component
export const CaptchaInput: React.FC<CaptchaInputProps> = ({
  useCaptchaWhichCaptchaIsActive,
  useCaptchaRequestTextCaptcha,
  onCaptchaChange,
  googleRecaptchaSiteKey = "your-site-key-here",
  className = "",
  error,
}) => {
  const { data: captchaTypeData, isLoading: isLoadingType } =
    useCaptchaWhichCaptchaIsActive();

  const captchaType = captchaTypeData?.data;

  const {
    data: textCaptchaData,
    isLoading: isLoadingTextCaptcha,
    refetch: refetchTextCaptcha,
  } = useCaptchaRequestTextCaptcha({
    query: {
      enabled: !!captchaType && captchaType === CaptchaTypeEnum.Text,
    },
  });

  // Handle Google reCAPTCHA token
  const handleGoogleCaptchaChange = (token: string) => {
    onCaptchaChange({ token });
  };

  // Handle image CAPTCHA input
  const handleImageCaptchaChange = (userInput: string) => {
    onCaptchaChange({
      secretKey: textCaptchaData?.data?.captchaSecretKey,
      userInput,
    });
  };

  // Handle refresh for image CAPTCHA
  const handleRefresh = () => {
    if (refetchTextCaptcha) {
      refetchTextCaptcha();
    }
  };

  if (isLoadingType) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <RefreshCw className="h-5 w-5 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Loading CAPTCHA...</span>
      </div>
    );
  }

  if (captchaType === CaptchaTypeEnum.None) {
    return null;
  }

  if (captchaType === CaptchaTypeEnum.GoogleRecaptchaV3) {
    return (
      <div className={className}>
        <GoogleReCaptchaProvider reCaptchaKey={googleRecaptchaSiteKey}>
          <GoogleCaptchaComponent onCaptchaChange={handleGoogleCaptchaChange} />
        </GoogleReCaptchaProvider>
      </div>
    );
  }

  if (captchaType === CaptchaTypeEnum.Text) {
    return (
      <div className={className}>
        <ImageCaptchaComponent
          captchaData={textCaptchaData}
          onRefresh={handleRefresh}
          onInputChange={handleImageCaptchaChange}
          isLoading={isLoadingTextCaptcha}
          error={error}
        />
      </div>
    );
  }

  return (
    <div className={`p-4 text-center text-gray-500 ${className}`}>
      <p>Unknown CAPTCHA type</p>
    </div>
  );
};
