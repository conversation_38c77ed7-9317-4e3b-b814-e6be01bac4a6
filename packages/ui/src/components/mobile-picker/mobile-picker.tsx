import {
  dayList,
  getYearList,
  monthList,
} from "@workspace/ui/components/mobile-picker/utils";
import { dateConverter } from "@workspace/ui/lib/dateConverter";
import { jalaliToGregorian } from "@workspace/ui/lib/jalaliToGregorian";
import React from "react";
import Picker from "react-mobile-picker";

export type TMobilePicker = {
  minYear?: number;
  maxYear?: number;
  value?: Date;
  onChange?: (value: Date) => void;
};

function MobilePicker(props: TMobilePicker) {
  const currenYearInPersian = new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    calendar: "persian",
  })
    .format(new Date())
    .replace(/\s.*$/, "");

  const {
    minYear = 1300,
    maxYear = currenYearInPersian || 1404,
    value,
    onChange,
  } = props;

  // Get the current date to use (provided value or current date)
  const currentDate = value || new Date();

  // Convert current date to <PERSON>ala<PERSON> for initial state
  const getJalaliFromDate = React.useCallback((date: Date) => {
    const jalali = dateConverter(date)
      .calendar("persian")
      .locale("en")
      .format("YYYY/M/D");

    const [jalaliYear, jalaliMonth, jalaliDay] = jalali.split("/").map(Number);

    return {
      day: Number(jalaliDay) || 1,
      month: Number(jalaliMonth) || 1,
      year: Number(jalaliYear) || 1389,
    };
  }, []);

  const [pickerValue, setPickerValue] = React.useState(() =>
    getJalaliFromDate(currentDate),
  );

  // Sync external value changes with internal state
  React.useEffect(() => {
    if (value) {
      setPickerValue(getJalaliFromDate(value));
    }
  }, [value, getJalaliFromDate]);

  const selections = {
    day: dayList,
    month: monthList,
    year: getYearList(Number(minYear), Number(maxYear)),
  } as const;

  // Handle picker value changes
  const handlePickerChange = (newPickerValue: typeof pickerValue) => {
    // If no onChange handler, don't allow changes (read-only)
    if (!onChange) {
      return;
    }

    // Update internal state
    setPickerValue(newPickerValue);

    // Convert Jalali to Gregorian and call onChange
    const gregoryDate = jalaliToGregorian(
      newPickerValue?.year || 1389,
      newPickerValue?.month || 1,
      newPickerValue?.day || 1,
    );
    onChange(gregoryDate);
  };

  return (
    <div
      style={{
        maskImage:
          "linear-gradient(to top, transparent, transparent 10%, white 50%, white 60%, transparent 90%, transparent)",
      }}
    >
      <Picker
        value={pickerValue}
        onChange={handlePickerChange}
        wheelMode="normal"
        className="*:last:[&>div]:bg-border-nautral-default!"
      >
        {(Object.keys(selections) as Array<keyof typeof selections>).map(
          (name) => (
            <Picker.Column
              key={name}
              name={name}
              className="*:text-text-nautral-default! *:text-sm *:font-normal *:select-none"
            >
              {selections[name]?.map((option) => (
                <Picker.Item key={option?.value} value={option?.value}>
                  {option?.label}
                </Picker.Item>
              ))}
            </Picker.Column>
          ),
        )}
      </Picker>
    </div>
  );
}

export default MobilePicker;
