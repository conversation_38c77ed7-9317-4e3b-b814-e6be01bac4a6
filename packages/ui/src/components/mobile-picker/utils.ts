export const monthList = Array.from({ length: 12 }, (_, i) => ({
  label: new Intl.DateTimeFormat("fa", { month: "long" }).format(
    new Date(0, i + 3, 1),
  ),
  value: Number((i + 1).toString()),
}));

export const dayList = Array.from({ length: 31 }, (_, i) => ({
  label: (i + 1).toString().padStart(2, "0"),
  value: Number((i + 1).toString()),
}));

export const getYearList = (minYear: number, maxYear: number) =>
  Array.from({ length: maxYear - minYear + 1 }, (_, i) => ({
    label: (minYear + i).toString(),
    value: Number((minYear + i).toString()),
  }));
