import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { SearchIcon, UserIcon } from "lucide-react";
import * as React from "react";

import { Input } from "../input/input";

describe("Input Component", () => {
  // Basic rendering tests
  it("renders an input with default variant", () => {
    render(<Input placeholder="Type something" />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
  });

  it("renders with a required indicator when required prop is true", () => {
    render(<Input title="Username" required />);

    // Use a more specific query to find the visible required indicator
    const requiredIndicator = screen.getByText("*", {
      selector: "span.text-text-error-default-2",
    });
    expect(requiredIndicator).toBeInTheDocument();
    expect(requiredIndicator.parentElement).toHaveTextContent("Username *");
  });

  it("renders with a required indicator when aria-required is true", () => {
    render(<Input title="Username" aria-required="true" />);

    // Use a more specific query to find the visible required indicator
    const requiredIndicator = screen.getByText("*", {
      selector: "span.text-text-error-default-2",
    });
    expect(requiredIndicator).toBeInTheDocument();
    expect(requiredIndicator.parentElement).toHaveTextContent("Username *");
  });

  // Variant and size tests
  it("applies the outline variant class by default", () => {
    render(<Input placeholder="Type something" />);

    const fieldset = screen
      .getByRole("textbox")
      .closest("div")
      ?.querySelector("fieldset");
    expect(fieldset).toHaveClass("border");
  });

  it("applies the underline variant class when specified", () => {
    render(<Input placeholder="Type something" variant="underline" />);

    const fieldset = screen
      .getByRole("textbox")
      .closest("div")
      ?.querySelector("fieldset");
    expect(fieldset).toHaveClass("border-b");
  });

  it("applies the correct size class", () => {
    const { rerender } = render(
      <Input placeholder="Type something" size="sm" />,
    );

    let rootElement = screen.getByRole("textbox").closest("div")?.parentElement;
    expect(rootElement).toHaveClass("h-8");

    rerender(<Input placeholder="Type something" size="md" />);
    rootElement = screen.getByRole("textbox").closest("div")?.parentElement;
    expect(rootElement).toHaveClass("h-10");

    rerender(<Input placeholder="Type something" size="lg" />);
    rootElement = screen.getByRole("textbox").closest("div")?.parentElement;
    expect(rootElement).toHaveClass("h-12");
  });

  // Adornment tests
  it("renders with a start adornment", () => {
    render(
      <Input
        placeholder="Search"
        startAdornment={<SearchIcon data-testid="search-icon" />}
      />,
    );

    expect(screen.getByTestId("search-icon")).toBeInTheDocument();
  });

  it("renders with an end adornment", () => {
    render(
      <Input
        placeholder="Username"
        endAdornment={<UserIcon data-testid="user-icon" />}
      />,
    );

    expect(screen.getByTestId("user-icon")).toBeInTheDocument();
  });

  it("renders with both start and end adornments", () => {
    render(
      <Input
        placeholder="Search users"
        startAdornment={<SearchIcon data-testid="search-icon" />}
        endAdornment={<UserIcon data-testid="user-icon" />}
      />,
    );

    expect(screen.getByTestId("search-icon")).toBeInTheDocument();
    expect(screen.getByTestId("user-icon")).toBeInTheDocument();
  });

  // Helper text tests
  it("renders with helper text", () => {
    render(
      <Input placeholder="Type something" helperText="This is a helper text" />,
    );

    expect(screen.getByText("This is a helper text")).toBeInTheDocument();
  });

  it("applies error styling to helper text when input is invalid", () => {
    render(
      <Input
        placeholder="Type something"
        helperText="This field is required"
        aria-invalid={true}
      />,
    );

    const helperText = screen.getByText("This field is required");
    expect(helperText.parentElement).toHaveAttribute("aria-invalid", "true");
  });

  // Disabled state tests
  it("renders in disabled state", () => {
    render(<Input placeholder="Type something" disabled />);

    const input = screen.getByRole("textbox");
    expect(input).toBeDisabled();

    const container = input.closest("div")?.parentElement;
    expect(container).toHaveClass("cursor-not-allowed");
  });

  it("renders helper text when disabled", () => {
    render(
      <Input placeholder="Type something" helperText="Helper text" disabled />,
    );

    // Just check that the helper text is rendered when the input is disabled
    const helperText = screen.getByText("Helper text");
    expect(helperText).toBeInTheDocument();
  });

  // Interaction tests
  it("handles input changes", async () => {
    const handleChange = jest.fn();
    render(<Input placeholder="Type something" onChange={handleChange} />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "Hello");

    expect(handleChange).toHaveBeenCalledTimes(5); // Once for each character
    expect(input).toHaveValue("Hello");
  });

  it("handles focus and blur events", () => {
    const handleFocus = jest.fn();
    const handleBlur = jest.fn();

    render(
      <Input
        placeholder="Type something"
        onFocus={handleFocus}
        onBlur={handleBlur}
      />,
    );

    const input = screen.getByRole("textbox");

    fireEvent.focus(input);
    expect(handleFocus).toHaveBeenCalledTimes(1);

    fireEvent.blur(input);
    expect(handleBlur).toHaveBeenCalledTimes(1);
  });

  // Controlled component tests
  it("works as a controlled component", () => {
    const TestComponent = () => {
      const [value, setValue] = React.useState("initial");
      return (
        <Input
          placeholder="Type something"
          value={value}
          onChange={(e) => setValue(e.target.value)}
          data-testid="controlled-input"
        />
      );
    };

    render(<TestComponent />);

    const input = screen.getByTestId("controlled-input");
    expect(input).toHaveValue("initial");

    fireEvent.change(input, { target: { value: "updated" } });
    expect(input).toHaveValue("updated");
  });

  // Accessibility tests
  it("passes through aria attributes", () => {
    render(
      <Input
        placeholder="Type something"
        aria-label="Input field"
        aria-describedby="description"
        aria-invalid={true}
        aria-required={true}
      />,
    );

    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("aria-label", "Input field");
    expect(input).toHaveAttribute("aria-describedby", "description");
    expect(input).toHaveAttribute("aria-invalid", "true");
    expect(input).toHaveAttribute("aria-required", "true");
  });

  it("applies aria-invalid to the root element", () => {
    render(<Input placeholder="Type something" aria-invalid={true} />);

    const rootElement = screen
      .getByRole("textbox")
      .closest("div")?.parentElement;
    expect(rootElement).toHaveAttribute("aria-invalid", "true");
  });
});
