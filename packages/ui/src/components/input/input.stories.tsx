import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Input } from "@workspace/ui/components/input";
import { SearchIcon, UserIcon } from "lucide-react";

const meta: Meta<typeof Input> = {
  title: "UI/Input/Input",
  component: Input,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["outline", "underline"],
      control: { type: "radio" },
    },
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "radio" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    "aria-invalid": {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
  args: {
    placeholder: "Type your username",
    title: "Username",
    required: true,
    helperText: "Helper text",
    "aria-invalid": false,
  },
};
export const InitialValue: Story = {
  args: {
    placeholder: "Type something",
    title: "field title",
    value: "amazing!",
  },
};

export const WithStartAdornment: Story = {
  args: {
    placeholder: "Search...",
    startAdornment: <SearchIcon className="h-4 w-4" />,
  },
};

export const WithEndAdornment: Story = {
  args: {
    placeholder: "Username",
    endAdornment: <UserIcon className="h-4 w-4" />,
  },
};

export const WithBothAdornments: Story = {
  args: {
    placeholder: "Search users...",
    startAdornment: <SearchIcon className="h-4 w-4" />,
    endAdornment: <UserIcon className="h-4 w-4" />,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Disabled input",
    disabled: true,
  },
};
