import * as React from "react";

import { cn, developmentOnly } from "@workspace/ui/lib/utils";
import { cva, VariantProps } from "class-variance-authority";

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    VariantProps<typeof rootClassName> {
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  className?: string;
  helperText?: string;
}

const rootClassName = cva(
  "group relative w-full h-full items-center gap-1 bg-transparent",
  {
    variants: {
      variant: {
        outline: "",
        underline: "",
      },
      size: {
        sm: "ps-2 pe-2 h-8",
        md: "ps-3 pe-2 h-10",
        lg: "ps-4 pe-2 h-12",
      },
    },
    defaultVariants: {
      variant: "outline",
      size: "md",
    },
  },
);

const fieldsetClassName = cva(
  "pointer-events-none absolute top-0 right-0 bottom-0 left-0 peer-placeholder-shown:p-0 peer-placeholder-shown:[&_legend]:hidden peer-focus-within:[&_legend]:block peer-placeholder-shown:[&_legend]:max-w-0 peer-focus-within:[&_legend]:max-w-full aria-invalid:border-border-error-default peer-disabled:border-border-nautral-disable",
  {
    variants: {
      variant: {
        outline:
          "border border-border-nautral-default peer-focus-within:border-2 peer-hover:border-2 peer-focus-within:border-border-primary-default peer-hover:border-border-primary-default rounded-sm",
        underline:
          "border-b border-border-nautral-default peer-focus-within:border-border-primary-default peer-hover:border-border-primary-default peer-focus-within:border-b-2 peer-hover:border-b-2",
      },
    },
    defaultVariants: {
      variant: "outline",
    },
  },
);

const titleClassName = cva(
  "text-text-nautral-secondary peer-disabled:text-text-nautral-disable pointer-events-none absolute -top-1.5 text-xs leading-3 transition-all peer-placeholder-shown:top-1/2 peer-focus-within:-top-1.5 peer-placeholder-shown:-translate-y-1/2 peer-focus-within:translate-none",
  {
    variants: {
      size: {
        sm: "start-4 peer-placeholder-shown:start-2 peer-focus-within:start-4 peer-placeholder-shown:text-xs peer-focus-within:text-xs",
        md: "start-4 peer-placeholder-shown:start-3 peer-focus-within:start-4 eer-placeholder-shown:text-sm peer-focus-within:text-xs",
        lg: "start-4 peer-placeholder-shown:text-sm peer-focus-within:text-xs",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
);

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      startAdornment,
      endAdornment,
      variant = "outline",
      size = "md",
      helperText,
      ...props
    },
    ref,
  ) => {
    return (
      <div
        className={cn(
          props?.disabled && "cursor-not-allowed",
          rootClassName({ variant, size, className }),
        )}
        aria-invalid={props?.["aria-invalid"]}
      >
        <div className="flex h-full w-full items-center gap-1">
          {startAdornment && (
            <div className="text-muted-foreground flex items-center justify-center">
              {startAdornment}
            </div>
          )}

          <input
            ref={ref}
            type={type}
            data-slot="input"
            className={cn(
              "peer file:text-foreground text-text-nautral-default selection:bg-primary selection:text-primary-foreground focus-within:placeholder:text-text-nautral-secondary h-full w-full min-w-0 bg-transparent text-sm outline-none file:inline-flex file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-transparent disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-transparent",
              className,
            )}
            {...props}
          />

          {endAdornment && (
            <div className="text-muted-foreground flex items-center justify-center">
              {endAdornment}
            </div>
          )}

          <fieldset
            className={fieldsetClassName({ variant, className }) + ""}
            aria-invalid={props?.["aria-invalid"]}
          >
            {/* just white space placeholder below the field title */}
            {(props?.title || props?.placeholder) && (
              <legend className="ms-2 h-0 ps-1 pe-2 text-xs opacity-0">
                {props?.title || props?.placeholder}{" "}
                {props?.required || props?.["aria-required"] ? (
                  <span>*</span>
                ) : (
                  ""
                )}
              </legend>
            )}
          </fieldset>

          {/* field title + placeholder */}
          {(props?.title || props?.placeholder) && (
            <div
              className={cn(
                startAdornment &&
                  "peer-placeholder-shown:ms-5 peer-focus-within:ms-0",
                titleClassName({ size, className }),
              )}
              data-testid={developmentOnly("input-title")}
            >
              {props?.title || props?.placeholder}{" "}
              {props?.required || props?.["aria-required"] ? (
                <span className="text-text-error-default-2">*</span>
              ) : (
                ""
              )}
            </div>
          )}
        </div>

        {helperText && (
          <div
            className={cn(
              "text-text-nautral-secondary aria-invalid:text-text-error-default-2 aria-disabled:text-text-nautral-disable",
              size === "sm" ? "text-[8px]" : "text-[10px]",
            )}
            aria-invalid={props?.["aria-invalid"]}
            aria-disabled={props?.disabled}
          >
            {helperText}
          </div>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";

export { Input };
