import * as React from "react";

import { Input } from "@workspace/ui/components/input";
import { cn } from "@workspace/ui/lib/utils";

// Helper function to format number with commas
function formatNumberWithCommas(value: string): string {
  if (!value) return "";

  // Remove any existing commas
  const plainNumber = value.replace(/,/g, "");

  // Handle decimal numbers
  const parts = plainNumber.split(".");
  const integerPart = parts[0] || "";
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : "";

  // Add commas for thousands separator (only to the integer part)
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Combine integer and decimal parts
  return `${formattedInteger}${decimalPart}`;
}

type InputNumberProps = Omit<React.ComponentProps<typeof Input>, "type"> & {
  value?: string | number;
  defaultValue?: string | number;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
};

const InputNumber = React.forwardRef<HTMLInputElement, InputNumberProps>(
  ({ className, value, defaultValue, onChange, ...props }, ref) => {
    const [internalValue, setInternalValue] = React.useState<string>(() => {
      // Initialize with defaultValue if provided
      if (defaultValue !== undefined) {
        const numStr = String(defaultValue);
        const sanitizedValue = numStr.replace(/[^\d.]/g, "");

        // Ensure only one decimal point exists
        const parts = sanitizedValue.split(".");
        const normalizedValue =
          parts.length > 1
            ? `${parts[0]}.${parts.slice(1).join("")}`
            : sanitizedValue;

        return formatNumberWithCommas(normalizedValue);
      }
      return "";
    });

    // Track if the component is controlled
    const isControlled = value !== undefined;

    // Format the displayed value with commas
    const displayValue = isControlled
      ? formatNumberWithCommas(String(value).replace(/[^\d.]/g, ""))
      : internalValue;

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      // Allow only English numbers (0-9) and decimal point
      const rawValue = e.target.value.replace(/[^\d.]/g, "");

      // Ensure only one decimal point exists
      const parts = rawValue.split(".");
      const sanitizedValue =
        parts.length > 1 ? `${parts[0]}.${parts.slice(1).join("")}` : rawValue;

      const formattedValue = formatNumberWithCommas(sanitizedValue);

      // For uncontrolled mode, update internal state
      if (!isControlled) {
        setInternalValue(formattedValue);
      }

      // Create a synthetic event with the formatted value
      if (onChange) {
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: sanitizedValue, // Pass the raw value without commas to the onChange handler
          },
        } as React.ChangeEvent<HTMLInputElement>;

        onChange(syntheticEvent);
      }
    };

    return (
      <Input
        ref={ref}
        type="text"
        className={cn(className)}
        value={displayValue}
        onChange={handleChange}
        {...props}
      />
    );
  },
);

InputNumber.displayName = "InputNumber";

export { InputNumber };
