import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import * as React from "react";

import { InputNumber } from "../input-number/input-number";

describe("InputNumber Component", () => {
  // Basic rendering tests
  it("renders an input with default props", () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("type", "text");
  });

  // Formatting tests
  it("formats initial value with commas for thousands", () => {
    render(<InputNumber defaultValue="1000" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,000");
  });

  it("formats decimal values correctly", () => {
    render(<InputNumber defaultValue="1234.56" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,234.56");
  });

  it("handles empty value", () => {
    render(<InputNumber />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("");
  });

  // Input validation tests
  it("allows only numeric input and decimal point", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "abc123.45def");

    expect(input).toHaveValue("123.45");
  });

  it("ensures only one decimal point exists", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "123.45.67");

    expect(input).toHaveValue("123.4567");
  });

  // Formatting during input tests
  it("formats numbers with commas as user types", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "1234567");

    expect(input).toHaveValue("1,234,567");
  });

  it("formats input correctly when typing", async () => {
    render(<InputNumber defaultValue="1000" />);

    const input = screen.getByRole("textbox") as HTMLInputElement;

    // Clear the input and type a new value
    await userEvent.clear(input);
    await userEvent.type(input, "12000");

    // Should be formatted with commas
    expect(input).toHaveValue("12,000");
  });

  // Controlled component tests
  it("works as a controlled component", () => {
    const handleChange = jest.fn();

    const TestComponent = () => {
      const [value, setValue] = React.useState("1000");

      const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
        handleChange(e);
      };

      return (
        <InputNumber
          placeholder="Enter a number"
          value={value}
          onChange={onChange}
          data-testid="controlled-input"
        />
      );
    };

    render(<TestComponent />);

    const input = screen.getByTestId("controlled-input");
    expect(input).toHaveValue("1,000");

    fireEvent.change(input, { target: { value: "2000" } });

    // The displayed value should have commas
    expect(input).toHaveValue("2,000");

    // The onChange handler should receive the raw value without commas
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: "2000" }),
      }),
    );
  });

  // Edge cases
  it("handles zero correctly", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "0");

    expect(input).toHaveValue("0");
  });

  it("handles leading decimal point", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, ".5");

    expect(input).toHaveValue(".5");
  });

  it("handles large numbers correctly", () => {
    render(<InputNumber defaultValue="1234567890.12" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,234,567,890.12");
  });

  // Prop passing tests
  it("passes through other props to the underlying Input component", () => {
    render(
      <InputNumber
        placeholder="Enter a number"
        disabled
        aria-label="Amount"
        data-testid="number-input"
      />,
    );

    const input = screen.getByTestId("number-input");
    expect(input).toBeDisabled();
    expect(input).toHaveAttribute("aria-label", "Amount");
  });
});
