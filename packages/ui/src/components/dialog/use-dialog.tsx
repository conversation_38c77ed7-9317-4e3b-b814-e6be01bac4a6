"use client";

import * as DialogPrimitive from "@radix-ui/react-dialog";
import { DialogContent } from "@workspace/ui/components/dialog";
import * as React from "react";
import { createPortal } from "react-dom";

export interface DialogConfig {
  id: string;
  content: React.ReactNode;
  onClose?: () => void;
  className?: string;
  overlayClassName?: string;
  open?: boolean;
}

interface DialogState extends DialogConfig {
  zIndex: number;
  isOpen: boolean;
  isClosing: boolean;
}

interface DialogContextValue {
  dialogs: DialogState[];
  openDialog: (config: Omit<DialogConfig, "id"> & { id?: string }) => string;
  closeDialog: (id: string) => void;
  closeAllDialogs: () => void;
  isDialogOpen: (id: string) => boolean;
}

const DialogContext = React.createContext<DialogContextValue | null>(null);

const BASE_Z_INDEX = 50;
const Z_INDEX_INCREMENT = 10;

export function DialogProvider({ children }: { children: React.ReactNode }) {
  const [dialogs, setDialogs] = React.useState<DialogState[]>([]);
  const [portalContainer, setPortalContainer] =
    React.useState<HTMLElement | null>(null);

  React.useEffect(() => {
    // Create or get the portal container
    let container = document.getElementById("dialog-portal-root");
    if (!container) {
      container = document.createElement("div");
      container.id = "dialog-portal-root";
      document.body.appendChild(container);
    }
    setPortalContainer(container);

    return () => {
      // Clean up empty portal container when component unmounts
      const existingContainer = document.getElementById("dialog-portal-root");
      if (
        existingContainer &&
        existingContainer.children.length === 0 &&
        existingContainer.parentNode
      ) {
        existingContainer.parentNode.removeChild(existingContainer);
      }
    };
  }, []);

  const openDialog = React.useCallback(
    (config: Omit<DialogConfig, "id"> & { id?: string }) => {
      const id =
        config.id ||
        `dialog-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      setDialogs((prev) => {
        const zIndex = BASE_Z_INDEX + prev.length * Z_INDEX_INCREMENT;
        const newDialog: DialogState = {
          ...config,
          id,
          zIndex,
          isOpen: true,
          isClosing: false,
        };
        return [...prev, newDialog];
      });
      return id;
    },
    [],
  );

  const closeDialog = React.useCallback((id: string) => {
    // First, mark the dialog as closing to trigger the close animation
    setDialogs((prev) =>
      prev.map((dialog) =>
        dialog.id === id
          ? { ...dialog, isOpen: false, isClosing: true }
          : dialog,
      ),
    );

    // After animation duration, remove the dialog from DOM
    setTimeout(() => {
      setDialogs((prev) => prev.filter((dialog) => dialog.id !== id));
    }, 200); // Radix UI dialog animation is typically 200ms
  }, []);

  const closeAllDialogs = React.useCallback(() => {
    // First, mark all dialogs as closing to trigger close animations
    setDialogs((prev) =>
      prev.map((dialog) => ({ ...dialog, isOpen: false, isClosing: true })),
    );

    // After animation duration, remove all dialogs from DOM
    setTimeout(() => {
      setDialogs([]);
    }, 200); // Radix UI dialog animation is typically 200ms
  }, []);

  const isDialogOpen = React.useCallback(
    (id: string) => {
      return dialogs.some((dialog) => dialog.id === id && dialog.isOpen);
    },
    [dialogs],
  );

  const contextValue: DialogContextValue = {
    dialogs,
    openDialog,
    closeDialog,
    closeAllDialogs,
    isDialogOpen,
  };

  return (
    <DialogContext.Provider value={contextValue}>
      {children}
      {portalContainer &&
        dialogs.map((dialog) =>
          createPortal(
            <DialogPortal
              key={dialog.id}
              dialog={dialog}
              onClose={() => closeDialog(dialog.id)}
            />,
            portalContainer,
          ),
        )}
    </DialogContext.Provider>
  );
}

interface DialogPortalProps {
  dialog: DialogState;
  onClose: () => void;
}

function DialogPortal({ dialog, onClose }: DialogPortalProps) {
  const handleOpenChange = (open: boolean) => {
    if (!open && !dialog.isClosing) {
      // Only trigger close if it's not already in closing state
      // This prevents double-closing when user clicks overlay or close button
      onClose();
      dialog.onClose?.();
    }
  };

  // Use the dialog's isOpen state for the radix dialog
  // When isClosing is true, isOpen will be false, triggering the close animation
  return (
    <div style={{ zIndex: dialog.zIndex }}>
      <DialogPrimitive.Root
        open={dialog.isOpen && !dialog.isClosing}
        onOpenChange={handleOpenChange}
      >
        <DialogContent>{dialog.content}</DialogContent>
      </DialogPrimitive.Root>
    </div>
  );
}

export function useDialog() {
  const context = React.useContext(DialogContext);
  if (!context) {
    throw new Error("useDialog must be used within a DialogProvider");
  }
  return context;
}

// Helper type for creating dialog content with shadcn components
export interface ShadcnDialogProps {
  children: React.ReactNode;
  className?: string;
}
