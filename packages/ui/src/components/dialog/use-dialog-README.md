# useDialog Hook

A powerful React hook for managing multiple dialogs with stacking support, built on top of React Portals and Radix UI Dialog.

## Features

- ✅ **Multiple Dialog Support**: Open multiple dialogs on top of each other
- ✅ **Automatic Z-Index Management**: Proper stacking order for overlapping dialogs
- ✅ **React Portal Integration**: Dialogs are rendered outside the component tree
- ✅ **Automatic DOM Cleanup**: Dialogs are removed from DOM after close animation completes
- ✅ **TypeScript Support**: Fully typed with excellent IntelliSense
- ✅ **Animation Support**: Smooth fade-in/out and zoom animations
- ✅ **Shadcn Integration**: Full compatibility with shadcn dialog components
- ✅ **Overlay Click to Close**: Click outside to close dialogs
- ✅ **Programmatic Control**: Open, close, and manage dialogs via API
- ✅ **Accessibility**: Built on Radix UI for full accessibility support

## Basic Usage

### 1. Wrap your app with DialogProvider

```tsx
import { DialogProvider } from "@workspace/ui/hooks/use-dialog";

function App() {
  return (
    <DialogProvider>
      <YourAppContent />
    </DialogProvider>
  );
}
```

### 2. Use the hook in your components

```tsx
import { useDialog } from "@workspace/ui/hooks/use-dialog";
import { Button } from "@workspace/ui/components/button";

function MyComponent() {
  const { openDialog, closeDialog } = useDialog();

  const handleOpenDialog = () => {
    openDialog({
      content: (
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">My Dialog</h2>
          <p>This is dialog content!</p>
          <Button onClick={() => closeDialog("my-dialog")}>
            Close
          </Button>
        </div>
      ),
      id: "my-dialog",
    });
  };

  return (
    <Button onClick={handleOpenDialog}>
      Open Dialog
    </Button>
  );
}
```

## API Reference

### DialogProvider

Wrap your application with this provider to enable dialog functionality.

```tsx
<DialogProvider>
  {children}
</DialogProvider>
```

### useDialog Hook

Returns an object with the following methods and properties:

#### Methods

- **`openDialog(config)`**: Opens a new dialog
- **`closeDialog(id)`**: Closes a specific dialog by ID
- **`closeAllDialogs()`**: Closes all open dialogs
- **`isDialogOpen(id)`**: Checks if a dialog is currently open

#### Properties

- **`dialogs`**: Array of currently open dialog states

### DialogConfig

Configuration object for opening dialogs:

```tsx
interface DialogConfig {
  id?: string;                    // Optional ID (auto-generated if not provided)
  content: React.ReactNode;       // Dialog content
  onClose?: () => void;          // Callback when dialog closes
  className?: string;            // Additional CSS classes for dialog
  overlayClassName?: string;     // Additional CSS classes for overlay
}
```

## Examples

### Using Shadcn Dialog Components

You can use the full shadcn dialog components with the useDialog hook for consistent styling and accessibility:

```tsx
import { 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from "@workspace/ui/components/dialog";

function ShadcnDialogExample() {
  const { openDialog, closeDialog } = useDialog();

  const openShadcnDialog = () => {
    openDialog({
      content: (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>User Profile</DialogTitle>
            <DialogDescription>
              Manage your account settings and preferences.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>Your dialog content goes here...</p>
          </div>
          <DialogFooter>
            <Button onClick={() => closeDialog("user-profile")}>
              Close
            </Button>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      ),
      id: "user-profile",
    });
  };

  return <Button onClick={openShadcnDialog}>Open User Profile</Button>;
}
```

### Multiple Dialogs

```tsx
function MultiDialogExample() {
  const { openDialog, closeDialog, dialogs } = useDialog();

  const openFirstDialog = () => {
    openDialog({
      id: "first",
      content: (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>First Dialog</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Button onClick={openSecondDialog}>
              Open Second Dialog
            </Button>
          </div>
        </DialogContent>
      ),
    });
  };

  const openSecondDialog = () => {
    openDialog({
      id: "second", 
      content: (
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Second Dialog</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>This dialog is on top!</p>
          </div>
        </DialogContent>
      ),
    });
  };

  return (
    <div>
      <Button onClick={openFirstDialog}>Open First Dialog</Button>
      <p>Open dialogs: {dialogs.length}</p>
    </div>
  );
}
```

### Using Simple Components

For easier development, use the provided simple dialog components:

```tsx
import { 
  SimpleDialog, 
  SimpleDialogHeader, 
  SimpleDialogContent, 
  SimpleDialogFooter 
} from "@workspace/ui/components/dialog";

function SimpleDialogExample() {
  const { openDialog, closeDialog } = useDialog();

  const openSimpleDialog = () => {
    openDialog({
      content: (
        <DialogContent>
          <SimpleDialog>
            <SimpleDialogHeader
              title="Simple Dialog"
              description="Easy to use dialog components"
              onClose={() => closeDialog("simple")}
            />
            <SimpleDialogContent>
              <p>Your content goes here...</p>
            </SimpleDialogContent>
            <SimpleDialogFooter>
              <Button onClick={() => closeDialog("simple")}>
                Close
              </Button>
            </SimpleDialogFooter>
          </SimpleDialog>
        </DialogContent>
      ),
      id: "simple",
    });
  };

  return <Button onClick={openSimpleDialog}>Open Simple Dialog</Button>;
}
```

## Animation Behavior

The useDialog hook properly handles close animations by:

1. **Preserving DOM during animation**: When a dialog is closed, it remains in the DOM during the close animation
2. **Automatic cleanup**: After the animation completes (200ms), the dialog is automatically removed from the DOM
3. **Smooth transitions**: Uses Radix UI's built-in animations for smooth fade and zoom effects
4. **No animation interruption**: Multiple dialogs can be closed simultaneously without animation conflicts

```tsx
// When you call closeDialog, the sequence is:
// 1. Dialog state changes to isOpen: false, isClosing: true
// 2. Radix UI triggers the close animation (200ms)
// 3. After animation completes, dialog is removed from DOM
closeDialog("my-dialog");
```

## Best Practices

1. **Always provide IDs** for dialogs you need to control programmatically
2. **Use the onClose callback** for cleanup when dialogs are closed
3. **Wrap your app at the root level** with DialogProvider
4. **Prefer shadcn dialog components** for full accessibility and consistent styling
5. **Use SimpleDialog components** for rapid prototyping and simple use cases
6. **Test dialog interactions** to ensure proper stacking and closing behavior
7. **Use DialogClose component** for proper close button behavior with shadcn components
8. **Don't interrupt animations** - avoid rapid open/close operations that might conflict with animations

## Available Shadcn Components

- `DialogContent` - Main dialog container with overlay
- `DialogHeader` - Header section with proper spacing
- `DialogTitle` - Accessible title component
- `DialogDescription` - Description with proper styling
- `DialogFooter` - Footer section for actions
- `DialogClose` - Proper close button component

## Storybook Examples

Check out the comprehensive Storybook examples in `dialog.stories.tsx` for more usage patterns and interactive demos.
