import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { DialogProvider } from "@workspace/ui/components/dialog/use-dialog";
import {
  DrawerClose,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@workspace/ui/components/drawer";
import { DrawerProvider } from "@workspace/ui/components/drawer/use-drawer";
import {
  ResponsiveModalProvider,
  useResponsiveModal,
} from "@workspace/ui/components/responsive-modal/use-responsive-modal";
import { Save, Settings, User } from "lucide-react";

const meta: Meta = {
  title: "UI/useResponsiveModal",
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A responsive modal hook that automatically switches between drawer (mobile) and dialog (desktop) based on screen size.",
      },
    },
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <DialogProvider>
        <DrawerProvider>
          <ResponsiveModalProvider>
            <div className="container max-w-4xl p-8">
              <Story />
            </div>
          </ResponsiveModalProvider>
        </DrawerProvider>
      </DialogProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj;

// Shadcn components example
function BasicUi() {
  const { openModal, closeModal, isMobile } = useResponsiveModal();

  const openShadcnModal = () => {
    openModal({
      content: isMobile ? (
        // Mobile: Use Drawer components
        <div>
          <DrawerHeader>
            <DrawerTitle>
              <User className="mr-2 inline h-4 w-4" />
              User Profile (Mobile)
            </DrawerTitle>
            <DrawerDescription>
              Manage your account settings and preferences on mobile.
            </DrawerDescription>
          </DrawerHeader>
          <div className="p-4">
            <div className="space-y-4">
              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Account Information</h4>
                <p className="text-muted-foreground text-sm">
                  Update your personal details and contact information.
                </p>
              </div>
              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Privacy Settings</h4>
                <p className="text-muted-foreground text-sm">
                  Control who can see your information and activity.
                </p>
              </div>
            </div>
          </div>
          <DrawerFooter>
            <Button onClick={() => closeModal("profile-modal")}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
            <DrawerClose asChild>
              <Button variant="outline">Cancel</Button>
            </DrawerClose>
          </DrawerFooter>
        </div>
      ) : (
        // Desktop: Use Dialog components
        <div>
          <DialogHeader>
            <DialogTitle>
              <User className="mr-2 inline h-4 w-4" />
              User Profile (Desktop)
            </DialogTitle>
            <DialogDescription>
              Manage your account settings and preferences on desktop.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Account Information</h4>
                <p className="text-muted-foreground text-sm">
                  Update your personal details and contact information.
                </p>
              </div>
              <div className="rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Privacy Settings</h4>
                <p className="text-muted-foreground text-sm">
                  Control who can see your information and activity.
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => closeModal("profile-modal")}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </div>
      ),
      id: "profile-modal",
      direction: "bottom", // Only affects mobile
    });
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">Shadcn Components</h3>
        <p className="text-muted-foreground mb-4">
          Using proper shadcn drawer and dialog components
        </p>
      </div>
      <Button onClick={openShadcnModal} className="w-full">
        <Settings className="mr-2 h-4 w-4" />
        Open Profile Settings
      </Button>
    </div>
  );
}

export const Basic: Story = {
  render: () => <BasicUi />,
};
