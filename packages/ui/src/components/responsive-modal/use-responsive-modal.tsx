"use client";

import * as React from "react";
import { useIsMobile } from "../../hooks/use-mobile";
import { useDialog, type DialogConfig } from "../dialog/use-dialog";
import { useDrawer, type DrawerConfig } from "../drawer/use-drawer";

// Merged configuration interface that supports both drawer and dialog options
export interface ResponsiveModalConfig {
  id?: string;
  content: React.ReactNode;
  onClose?: () => void;
  className?: string;
  overlayClassName?: string;

  // Drawer-specific options (only used on mobile)
  direction?: "top" | "bottom" | "left" | "right";
  dismissible?: boolean;

  // Future extensibility for dialog-specific options
  // dialogSpecificOption?: boolean;
}

interface ResponsiveModalContextValue {
  openModal: (config: ResponsiveModalConfig) => string;
  closeModal: (id: string) => void;
  closeAllModals: () => void;
  isModalOpen: (id: string) => boolean;
  isMobile: boolean;
  // Expose underlying hook data for advanced use cases
  drawers?: ReturnType<typeof useDrawer>["drawers"];
  dialogs?: ReturnType<typeof useDialog>["dialogs"];
}

const ResponsiveModalContext =
  React.createContext<ResponsiveModalContextValue | null>(null);

export function ResponsiveModalProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const drawer = useDrawer();
  const dialog = useDialog();
  const isMobile = useIsMobile();

  const openModal = React.useCallback(
    (config: ResponsiveModalConfig) => {
      if (isMobile) {
        // Convert ResponsiveModalConfig to DrawerConfig
        const drawerConfig: Omit<DrawerConfig, "id"> & { id?: string } = {
          id: config.id,
          content: config.content,
          direction: config.direction || "bottom", // Default to bottom on mobile
          dismissible: config.dismissible ?? true, // Default to dismissible
          onClose: config.onClose,
          className: config.className,
          overlayClassName: config.overlayClassName,
        };
        return drawer.openDrawer(drawerConfig);
      } else {
        // Convert ResponsiveModalConfig to DialogConfig
        const dialogConfig: Omit<DialogConfig, "id"> & { id?: string } = {
          id: config.id,
          content: config.content,
          onClose: config.onClose,
          className: config.className,
          overlayClassName: config.overlayClassName,
        };
        return dialog.openDialog(dialogConfig);
      }
    },
    [isMobile, drawer, dialog],
  );

  const closeModal = React.useCallback(
    (id: string) => {
      if (isMobile) {
        drawer.closeDrawer(id);
      } else {
        dialog.closeDialog(id);
      }
    },
    [isMobile, drawer, dialog],
  );

  const closeAllModals = React.useCallback(() => {
    if (isMobile) {
      drawer.closeAllDrawers();
    } else {
      dialog.closeAllDialogs();
    }
  }, [isMobile, drawer, dialog]);

  const isModalOpen = React.useCallback(
    (id: string) => {
      if (isMobile) {
        return drawer.isDrawerOpen(id);
      } else {
        return dialog.isDialogOpen(id);
      }
    },
    [isMobile, drawer, dialog],
  );

  const contextValue: ResponsiveModalContextValue = {
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
    isMobile,
    drawers: drawer.drawers,
    dialogs: dialog.dialogs,
  };

  return (
    <ResponsiveModalContext.Provider value={contextValue}>
      {children}
    </ResponsiveModalContext.Provider>
  );
}

export function useResponsiveModal() {
  const context = React.useContext(ResponsiveModalContext);
  if (!context) {
    throw new Error(
      "useResponsiveModal must be used within a ResponsiveModalProvider",
    );
  }
  return context;
}

// Helper type for creating responsive modal content with shadcn components
export interface ResponsiveModalContentProps {
  children: React.ReactNode;
  className?: string;
}

// Re-export types for convenience
export type { DialogConfig, DrawerConfig };
