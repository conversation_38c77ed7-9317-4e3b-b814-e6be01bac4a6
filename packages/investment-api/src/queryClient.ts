/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  isServer,
  QueryClient,
  QueryFunctionContext,
} from "@tanstack/react-query";
import { apiService } from "./apiService";

export const defaultQueryFn = async ({
  queryKey,
  signal,
}: QueryFunctionContext & { queryKey: any }) => {
  if (!queryKey[0]) throw new Error("queryKey[0] is empty");

  const { data } = await apiService.get(queryKey[0], {
    ...(signal && { signal }),
  });
  return data;
};

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // With SSR, we usually want to set some default staleTime
        // above 0 to avoid refetching immediately on the client
        staleTime: 60 * 1000,
        queryFn: defaultQueryFn,
      },
    },
  });
}

let browserQueryClient: QueryClient | undefined = undefined;

export function getQueryClient() {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient();
  } else {
    // Browser: make a new query client if we don't already have one
    // This is very important, so we don't re-make a new client if React
    // suspends during the initial render. This may not be needed if we
    // have a suspense boundary BELOW the creation of the query client
    if (!browserQueryClient) browserQueryClient = makeQueryClient();
    return browserQueryClient;
  }
}
