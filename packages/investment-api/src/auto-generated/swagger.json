{"x-generator": "NSwag v14.2.0.0 (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Investment API", "description": "Saba Tamin Investment Project", "termsOfService": "https://otcsaba.ir", "contact": {"name": "Contact Us", "url": "https://otcsaba.ir"}, "license": {"name": "Terms And Services", "url": "https://otcsaba.ir"}, "version": "v1"}, "servers": [{"url": "http://***************:5100"}], "paths": {"/api/general/v1/Auth/GetLoginMethod/{Mobile}": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "دریافت روش لاگین کاربر", "operationId": "Auth_GetLoginMethod", "parameters": [{"name": "Mobile", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfLoginMethodResponse"}}}}}}}, "/api/general/v1/Auth/SendLoginOTP": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ارسال کد یکبار مصرف", "operationId": "Auth_SendLoginOTP", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"secretKey": {"type": "string", "format": "guid"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetOtpResponse"}}}}}}}, "/api/general/v1/Auth/LoginByOTP": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کد یکبار مصرف", "operationId": "Auth_LoginByOTP", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"secretKey": {"type": "string", "format": "guid"}, "otp": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Auth/LoginByPassword": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کلمه عبور", "operationId": "Auth_LoginByPassword", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"secretKey": {"type": "string", "format": "guid"}, "password": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Auth/Logout": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "خروج از سیستم", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Captcha/WhichCaptchaIsActive": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "کدام نوع کپچا فعال است", "operationId": "Captcha_WhichCaptchaIsActive", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfCaptchaTypeEnum"}}}}}}}, "/api/general/v1/Captcha/RequestTextCaptcha": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "درخواست کپچای متنی", "operationId": "Captcha_RequestTextCaptcha", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfRequestCaptchaOutputDTO"}}}}}}}, "/api/customer/v1/ChangePassword/CheckActorUserPassword": {"post": {"tags": ["ChangePassword"], "summary": "چک کردن کلمه عبور", "operationId": "ChangePassword_CheckActorUserPassword", "requestBody": {"x-name": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckActorUserPasswordQuery"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/customer/v1/ChangePassword/ChangeActorUserPassword": {"put": {"tags": ["ChangePassword"], "summary": "تغییر کلمه عبور", "operationId": "ChangePassword_ChangeActorUserPassword", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeActorUserPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/FinancialInstitutes/GetAll": {"get": {"tags": ["FinancialInstitutes"], "summary": "لیست نهادهای مالی", "operationId": "FinancialInstitutes_GetAll", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfGetAllFinancialInstitutesResponse"}}}}}}}, "/api/customer/v1/ForgetPassword/SendOTP": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - ارسال کد یکبار مصرف", "operationId": "ForgetPassword_SendOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordSendOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetOtpWithSecretKeyResponse"}}}}}}}, "/api/customer/v1/ForgetPassword/CheckOTP": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - چک کردن کد یکبار مصرف", "operationId": "ForgetPassword_CheckOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordCheckOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/customer/v1/ForgetPassword/SetNewPassword": {"post": {"tags": ["ForgetPassword"], "summary": "فراموشی کلمه عبور - تنظیم کلمه عبور جدید", "operationId": "ForgetPassword_SetNewPassword", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgetPasswordSetNewPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/general/v1/Profile": {"get": {"tags": ["Profile"], "summary": "پروفایل کاربری", "operationId": "Profile_GetLoggedInUserProfile", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetLoggedInUserProfileResponse"}}}}}}}, "/api/general/v1/Sejam/IsSejami": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "چک کردن سجامی بودن کاربر", "operationId": "Sejam_IsSejami", "requestBody": {"x-name": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IsSejamiQuery"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/KycOTP": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ارسال کد یکبار مصرف سجام", "operationId": "Sejam_KycOTP", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KycOTPCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/ProfileInquiry": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "دریا<PERSON>ت کلید امنیتی پروفایل سجام", "operationId": "Sejam_ProfileInquiry", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileInquiryCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGuid"}}}}}}}, "/api/general/v1/Sejam/ProfileInquiryShow": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "نمایش پروفایل سجام با کلید امنیتی", "operationId": "Sejam_ProfileInquiryShow", "parameters": [{"name": "FinancialInstituteId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfProfileResponse"}}}}}}}, "/api/general/v1/Sejam/SetAsCustomerProfile": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ایجاد پروفایل کاربری برای ویزیتور", "operationId": "Sejam_SetAsCustomerProfile", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SetAsCustomerProfileCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/general/v1/Sejam/UpdateProfile": {"put": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "ویرایش پروفایل مشتری", "operationId": "Sejam_UpdateProfile", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Auth/LoginByPassword": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "ورود به سیستم با کلمه عبور", "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Auth/Logout": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "خروج از سیستم", "operationId": "Auth_Logout2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/Captcha/WhichCaptchaIsActive": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "کدام نوع کپچا فعال است", "operationId": "Captcha_WhichCaptchaIsActive2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfCaptchaTypeEnum"}}}}}}}, "/api/backoffice/v1/Captcha/RequestTextCaptcha": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "درخواست کپچای متنی", "operationId": "Captcha_RequestTextCaptcha2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfRequestCaptchaOutputDTO"}}}}}}}, "/api/backoffice/v1/ChangePassword": {"put": {"tags": ["ChangePassword"], "summary": "تغییر کلمه عبور", "operationId": "ChangePassword_ChangeActorUserPassword2", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeActorUserPasswordCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}, "/api/backoffice/v1/FinancialInstitutes/GetAll": {"get": {"tags": ["FinancialInstitutes"], "summary": "لیست نهادهای مالی", "operationId": "FinancialInstitutes_GetAll2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfListOfGetAllFinancialInstitutesResponse"}}}}}}}, "/api/backoffice/v1/Profile": {"get": {"tags": ["Profile"], "summary": "پروفایل کاربری", "operationId": "Profile_GetLoggedInUserProfile2", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfGetLoggedInUserProfileResponse"}}}}}}}, "/api/backoffice/v1/Settings/GetSystemSettings": {"get": {"tags": ["Settings"], "summary": "دریافت تنظیمات سیستم", "operationId": "Settings_GetSystemSettings", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfSystemSettingsResponse"}}}}}}}, "/api/backoffice/v1/Settings/EditSystemSettings": {"put": {"tags": ["Settings"], "summary": "ویرایش تنظیمات سیستم", "operationId": "Settings_EditSystemSettings", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EditSystemSettingsCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResultOfBoolean"}}}}}}}}, "components": {"schemas": {"ApiResultOfLoginMethodResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/LoginMethodResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "LoginMethodResponse": {"type": "object", "additionalProperties": false, "properties": {"grantType": {"$ref": "#/components/schemas/GrantTypeEnum"}, "secretKey": {"type": "string", "format": "guid"}}}, "GrantTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Password", "OTP"], "enum": [0, 100, 200]}, "ApiResultOfGetOtpResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetOtpResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetOtpResponse": {"type": "object", "additionalProperties": false, "properties": {"otpSendDate": {"type": "string", "format": "date-time"}, "otpExpirationDate": {"type": "string", "format": "date-time"}}}, "ApiResultOfBoolean": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "boolean"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ApiResultOfCaptchaTypeEnum": {"type": "object", "additionalProperties": false, "properties": {"data": {"$ref": "#/components/schemas/CaptchaTypeEnum"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "CaptchaTypeEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "GoogleRecaptchaV3", "Text"], "enum": [0, 100, 200]}, "ApiResultOfRequestCaptchaOutputDTO": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/RequestCaptchaOutputDTO"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "RequestCaptchaOutputDTO": {"type": "object", "additionalProperties": false, "properties": {"captchaImage": {"type": "string"}, "captchaSecretKey": {"type": "string"}}}, "CheckActorUserPasswordQuery": {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string"}}}, "ChangeActorUserPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"oldPassword": {"type": "string"}, "newPassword": {"type": "string"}, "confirmNewPassword": {"type": "string"}}}, "ApiResultOfListOfGetAllFinancialInstitutesResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/GetAllFinancialInstitutesResponse"}}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetAllFinancialInstitutesResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "symbolName": {"type": "string"}, "fullName": {"type": "string"}, "financialInstituteType": {"$ref": "#/components/schemas/FinancialInstituteType"}}}, "FinancialInstituteType": {"type": "integer", "description": "", "x-enumNames": ["None", "Fund"], "enum": [0, 100]}, "ApiResultOfGetOtpWithSecretKeyResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetOtpWithSecretKeyResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetOtpWithSecretKeyResponse": {"type": "object", "additionalProperties": false, "properties": {"otpSendDate": {"type": "string", "format": "date-time"}, "otpExpirationDate": {"type": "string", "format": "date-time"}, "secretKey": {"type": "string", "format": "guid"}}}, "ForgetPasswordSendOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"mobile": {"type": "string"}}}, "ApiResultOfGuid": {"type": "object", "additionalProperties": false, "properties": {"data": {"type": "string", "format": "guid"}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ForgetPasswordCheckOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"secretKey": {"type": "string", "format": "guid"}, "otp": {"type": "string"}}}, "ForgetPasswordSetNewPasswordCommand": {"type": "object", "additionalProperties": false, "properties": {"secretKey": {"type": "string", "format": "guid"}, "newPassword": {"type": "string"}, "confirmNewPassword": {"type": "string"}}}, "ApiResultOfGetLoggedInUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GetLoggedInUserProfileResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "GetLoggedInUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"role": {"$ref": "#/components/schemas/RoleEnum"}, "userProfile": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/UserProfileResponse"}]}, "visitorUserProfile": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/VisitorUserProfileResponse"}]}}}, "RoleEnum": {"type": "integer", "description": "", "x-enumNames": ["None", "Administrator", "Visitor", "Customer"], "enum": [0, 100, 200, 300]}, "UserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "userName": {"type": "string"}, "mobileNumber": {"type": "string"}, "isActive": {"type": "boolean"}, "role": {"$ref": "#/components/schemas/RoleEnum"}, "roleName": {"type": "string"}, "roleDescription": {"type": "string"}, "userSystemProfile": {"$ref": "#/components/schemas/UserSystemProfileResponse"}, "userSejamProfile": {"$ref": "#/components/schemas/UserSejamProfileResponse"}}}, "UserSystemProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}}}, "UserSejamProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"createDate": {"type": "string", "format": "date-time"}, "mobile": {"type": "integer", "format": "int64"}, "email": {"type": "string", "nullable": true}, "uniqueIdentifier": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "privatePerson": {"$ref": "#/components/schemas/SejamPrivatePersonResponse"}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/SejamAddressResponse"}}, "tradingCodes": {"type": "array", "items": {"$ref": "#/components/schemas/SejamTradingCodeResponse"}}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/SejamBankingAccountResponse"}}, "jobInfo": {"$ref": "#/components/schemas/SejamJobInfoResponse"}, "financialInfo": {"$ref": "#/components/schemas/SejamFinancialInfoResponse"}}}, "SejamPrivatePersonResponse": {"type": "object", "additionalProperties": false, "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fatherName": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "seriShChar": {"type": "string", "nullable": true}, "seriSh": {"type": "string", "nullable": true}, "serial": {"type": "string", "nullable": true}, "shNumber": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "placeOfIssue": {"type": "string", "nullable": true}, "placeOfBirth": {"type": "string", "nullable": true}}}, "SejamAddressResponse": {"type": "object", "additionalProperties": false, "properties": {"postalCode": {"type": "string", "nullable": true}, "countryId": {"type": "integer", "format": "int32", "nullable": true}, "countryName": {"type": "string", "nullable": true}, "provinceId": {"type": "integer", "format": "int32", "nullable": true}, "provinceName": {"type": "string", "nullable": true}, "cityId": {"type": "integer", "format": "int32", "nullable": true}, "cityName": {"type": "string", "nullable": true}, "sectionId": {"type": "integer", "format": "int32", "nullable": true}, "sectionName": {"type": "string", "nullable": true}, "cityPrefix": {"type": "string", "nullable": true}, "remnantAddress": {"type": "string", "nullable": true}, "alley": {"type": "string", "nullable": true}, "plaque": {"type": "string", "nullable": true}, "tel": {"type": "string", "nullable": true}, "countryPrefix": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "emergencyTel": {"type": "string", "nullable": true}, "emergencyTelCityPrefix": {"type": "string", "nullable": true}, "emergencyTelCountryPrefix": {"type": "string", "nullable": true}, "faxPrefix": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}}, "SejamTradingCodeResponse": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "nullable": true}, "firstPart": {"type": "string", "nullable": true}, "secondPart": {"type": "string", "nullable": true}, "thirdPart": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}}, "SejamBankingAccountResponse": {"type": "object", "additionalProperties": false, "properties": {"accountNumber": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "sheba": {"type": "string", "nullable": true}, "bankId": {"type": "integer", "format": "int32", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "branchCode": {"type": "string", "nullable": true}, "branchName": {"type": "string", "nullable": true}, "branchCityId": {"type": "integer", "format": "int32", "nullable": true}, "branchCityName": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean", "nullable": true}, "modifiedDate": {"type": "string", "format": "date-time", "nullable": true}}}, "SejamJobInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"employmentDate": {"type": "string", "format": "date-time", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "companyAddress": {"type": "string", "nullable": true}, "companyPostalCode": {"type": "string", "nullable": true}, "companyEmail": {"type": "string", "nullable": true}, "companyWebSite": {"type": "string", "nullable": true}, "companyCityPrefix": {"type": "string", "nullable": true}, "companyPhone": {"type": "string", "nullable": true}, "position": {"type": "string", "nullable": true}, "companyFaxPrefix": {"type": "string", "nullable": true}, "companyFax": {"type": "string", "nullable": true}, "jobId": {"type": "integer", "format": "int32", "nullable": true}, "jobTitle": {"type": "string", "nullable": true}, "jobDescription": {"type": "string", "nullable": true}}}, "SejamFinancialInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"assetsValue": {"type": "integer", "format": "int64", "nullable": true}, "inComingAverage": {"type": "integer", "format": "int64", "nullable": true}, "sExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "cExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "outExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "transactionLevel": {"type": "string", "nullable": true}, "tradingKnowledgeLevel": {"type": "string", "nullable": true}, "companyPurpose": {"type": "string", "nullable": true}, "referenceRateCompany": {"type": "string", "nullable": true}, "rateDate": {"type": "string", "format": "date-time", "nullable": true}, "rate": {"type": "integer", "format": "int64", "nullable": true}}}, "VisitorUserProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"visitorUserId": {"type": "string", "format": "guid"}, "createDate": {"type": "string", "format": "date-time"}, "mobileNumber": {"type": "string"}}}, "IsSejamiQuery": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}}}, "KycOTPCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}}}, "ProfileInquiryCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "nationalCode": {"type": "string"}, "kycOtpCode": {"type": "string"}}}, "ApiResultOfProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ProfileResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "ProfileResponse": {"type": "object", "additionalProperties": false, "properties": {"mobile": {"type": "integer", "format": "int64"}, "email": {"type": "string", "nullable": true}, "uniqueIdentifier": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "privatePerson": {"$ref": "#/components/schemas/PrivatePersonDTO"}, "legalPerson": {"nullable": true}, "addresses": {"type": "array", "items": {"$ref": "#/components/schemas/AddressDTO"}}, "tradingCodes": {"type": "array", "items": {"$ref": "#/components/schemas/TradingCodeDTO"}}, "agent": {"nullable": true}, "accounts": {"type": "array", "items": {"$ref": "#/components/schemas/BankingAccountDTO"}}, "jobInfo": {"$ref": "#/components/schemas/JobInfoDTO"}, "financialInfo": {"$ref": "#/components/schemas/FinancialInfoDTO"}, "legalPersonShareholders": {"type": "array", "items": {}}, "legalPersonStakeholders": {"type": "array", "items": {}}}}, "PrivatePersonDTO": {"type": "object", "additionalProperties": false, "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "fatherName": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "seriShChar": {"type": "string", "nullable": true}, "seriSh": {"type": "string", "nullable": true}, "serial": {"type": "string", "nullable": true}, "shNumber": {"type": "string", "nullable": true}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}, "placeOfIssue": {"type": "string", "nullable": true}, "placeOfBirth": {"type": "string", "nullable": true}, "signatureFile": {"nullable": true}}}, "AddressDTO": {"type": "object", "additionalProperties": false, "properties": {"postalCode": {"type": "string", "nullable": true}, "country": {"$ref": "#/components/schemas/CountryDTO"}, "province": {"$ref": "#/components/schemas/ProvinceDTO"}, "city": {"$ref": "#/components/schemas/CityDTO"}, "section": {"$ref": "#/components/schemas/SectionDTO"}, "cityPrefix": {"type": "string", "nullable": true}, "remnantAddress": {"type": "string", "nullable": true}, "alley": {"type": "string", "nullable": true}, "plaque": {"type": "string", "nullable": true}, "tel": {"type": "string", "nullable": true}, "countryPrefix": {"type": "string", "nullable": true}, "mobile": {"type": "string", "nullable": true}, "emergencyTel": {"type": "string", "nullable": true}, "emergencyTelCityPrefix": {"type": "string", "nullable": true}, "emergencyTelCountryPrefix": {"type": "string", "nullable": true}, "faxPrefix": {"type": "string", "nullable": true}, "fax": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}}, "CountryDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "ProvinceDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "CityDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "SectionDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "TradingCodeDTO": {"type": "object", "additionalProperties": false, "properties": {"type": {"type": "string", "nullable": true}, "firstPart": {"type": "string", "nullable": true}, "secondPart": {"type": "string", "nullable": true}, "thirdPart": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}}, "BankingAccountDTO": {"type": "object", "additionalProperties": false, "properties": {"accountNumber": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "sheba": {"type": "string", "nullable": true}, "bank": {"$ref": "#/components/schemas/BankDTO"}, "branchCode": {"type": "string", "nullable": true}, "branchName": {"type": "string", "nullable": true}, "branchCity": {"$ref": "#/components/schemas/BranchCityDTO"}, "isDefault": {"type": "boolean", "nullable": true}, "modifiedDate": {"type": "string", "format": "date-time", "nullable": true}}}, "BankDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "BranchCityDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}}}, "JobInfoDTO": {"type": "object", "additionalProperties": false, "properties": {"employmentDate": {"type": "string", "format": "date-time", "nullable": true}, "companyName": {"type": "string", "nullable": true}, "companyAddress": {"type": "string", "nullable": true}, "companyPostalCode": {"type": "string", "nullable": true}, "companyEmail": {"type": "string", "nullable": true}, "companyWebSite": {"type": "string", "nullable": true}, "companyCityPrefix": {"type": "string", "nullable": true}, "companyPhone": {"type": "string", "nullable": true}, "position": {"type": "string", "nullable": true}, "companyFaxPrefix": {"type": "string", "nullable": true}, "companyFax": {"type": "string", "nullable": true}, "job": {"$ref": "#/components/schemas/JobDTO"}, "jobDescription": {"type": "string", "nullable": true}}}, "JobDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32", "nullable": true}, "title": {"type": "string", "nullable": true}}}, "FinancialInfoDTO": {"type": "object", "additionalProperties": false, "properties": {"assetsValue": {"type": "integer", "format": "int64", "nullable": true}, "inComingAverage": {"type": "integer", "format": "int64", "nullable": true}, "sExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "cExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "outExchangeTransaction": {"type": "integer", "format": "int64", "nullable": true}, "transactionLevel": {"type": "string", "nullable": true}, "tradingKnowledgeLevel": {"type": "string", "nullable": true}, "companyPurpose": {"type": "string", "nullable": true}, "referenceRateCompany": {"type": "string", "nullable": true}, "rateDate": {"type": "string", "format": "date-time", "nullable": true}, "rate": {"type": "integer", "format": "int64", "nullable": true}, "financialBrokers": {"type": "array", "items": {}}}}, "SetAsCustomerProfileCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "secretKey": {"type": "string", "format": "guid"}, "password": {"type": "string"}, "confirmPassword": {"type": "string"}}}, "UpdateProfileCommand": {"type": "object", "additionalProperties": false, "properties": {"financialInstituteId": {"type": "string", "format": "guid"}, "secretKey": {"type": "string", "format": "guid"}}}, "ApiResultOfSystemSettingsResponse": {"type": "object", "additionalProperties": false, "properties": {"data": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/SystemSettingsResponse"}]}, "isSuccess": {"type": "boolean"}, "errorCode": {"type": "string", "nullable": true}, "errorMessage": {"type": "string", "nullable": true}}}, "SystemSettingsResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "captchaType": {"$ref": "#/components/schemas/CaptchaTypeEnum"}}}, "EditSystemSettingsCommand": {"type": "object", "additionalProperties": false, "properties": {"captchaTypeEnum": {"$ref": "#/components/schemas/CaptchaTypeEnum"}}}}}}