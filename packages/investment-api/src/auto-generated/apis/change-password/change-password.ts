/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation } from "@tanstack/react-query";
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ChangeActorUserPasswordCommand,
  CheckActorUserPasswordQuery,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary چک کردن کلمه عبور
 */
export const changePasswordCheckActorUserPassword = (
  checkActorUserPasswordQuery: CheckActorUserPasswordQuery,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/customer/v1/ChangePassword/CheckActorUserPassword`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: checkActorUserPasswordQuery,
      signal,
    },
    options,
  );
};

export const getChangePasswordCheckActorUserPasswordMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>,
    TError,
    { data: CheckActorUserPasswordQuery },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>,
  TError,
  { data: CheckActorUserPasswordQuery },
  TContext
> => {
  const mutationKey = ["changePasswordCheckActorUserPassword"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>,
    { data: CheckActorUserPasswordQuery }
  > = (props) => {
    const { data } = props ?? {};

    return changePasswordCheckActorUserPassword(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ChangePasswordCheckActorUserPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>
>;
export type ChangePasswordCheckActorUserPasswordMutationBody =
  CheckActorUserPasswordQuery;
export type ChangePasswordCheckActorUserPasswordMutationError =
  ErrorType<unknown>;

/**
 * @summary چک کردن کلمه عبور
 */
export const useChangePasswordCheckActorUserPassword = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>,
      TError,
      { data: CheckActorUserPasswordQuery },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof changePasswordCheckActorUserPassword>>,
  TError,
  { data: CheckActorUserPasswordQuery },
  TContext
> => {
  const mutationOptions =
    getChangePasswordCheckActorUserPasswordMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary تغییر کلمه عبور
 */
export const changePasswordChangeActorUserPassword = (
  changeActorUserPasswordCommand: ChangeActorUserPasswordCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/customer/v1/ChangePassword/ChangeActorUserPassword`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: changeActorUserPasswordCommand,
    },
    options,
  );
};

export const getChangePasswordChangeActorUserPasswordMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>,
    TError,
    { data: ChangeActorUserPasswordCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>,
  TError,
  { data: ChangeActorUserPasswordCommand },
  TContext
> => {
  const mutationKey = ["changePasswordChangeActorUserPassword"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>,
    { data: ChangeActorUserPasswordCommand }
  > = (props) => {
    const { data } = props ?? {};

    return changePasswordChangeActorUserPassword(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ChangePasswordChangeActorUserPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>
>;
export type ChangePasswordChangeActorUserPasswordMutationBody =
  ChangeActorUserPasswordCommand;
export type ChangePasswordChangeActorUserPasswordMutationError =
  ErrorType<unknown>;

/**
 * @summary تغییر کلمه عبور
 */
export const useChangePasswordChangeActorUserPassword = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>,
      TError,
      { data: ChangeActorUserPasswordCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword>>,
  TError,
  { data: ChangeActorUserPasswordCommand },
  TContext
> => {
  const mutationOptions =
    getChangePasswordChangeActorUserPasswordMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary تغییر کلمه عبور
 */
export const changePasswordChangeActorUserPassword2 = (
  changeActorUserPasswordCommand: ChangeActorUserPasswordCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/ChangePassword`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: changeActorUserPasswordCommand,
    },
    options,
  );
};

export const getChangePasswordChangeActorUserPassword2MutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>,
    TError,
    { data: ChangeActorUserPasswordCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>,
  TError,
  { data: ChangeActorUserPasswordCommand },
  TContext
> => {
  const mutationKey = ["changePasswordChangeActorUserPassword2"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>,
    { data: ChangeActorUserPasswordCommand }
  > = (props) => {
    const { data } = props ?? {};

    return changePasswordChangeActorUserPassword2(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ChangePasswordChangeActorUserPassword2MutationResult = NonNullable<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>
>;
export type ChangePasswordChangeActorUserPassword2MutationBody =
  ChangeActorUserPasswordCommand;
export type ChangePasswordChangeActorUserPassword2MutationError =
  ErrorType<unknown>;

/**
 * @summary تغییر کلمه عبور
 */
export const useChangePasswordChangeActorUserPassword2 = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>,
      TError,
      { data: ChangeActorUserPasswordCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof changePasswordChangeActorUserPassword2>>,
  TError,
  { data: ChangeActorUserPasswordCommand },
  TContext
> => {
  const mutationOptions =
    getChangePasswordChangeActorUserPassword2MutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
