/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfGuid,
  ApiResultOfProfileResponse,
  IsSejamiQuery,
  KycOTPCommand,
  ProfileInquiryCommand,
  SejamProfileInquiryShowParams,
  SetAsCustomerProfileCommand,
  UpdateProfileCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary چک کردن سجامی بودن کاربر
 */
export const sejamIsSejami = (
  isSejamiQuery: IsSejamiQuery,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Sejam/IsSejami`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: isSejamiQuery,
      signal,
    },
    options,
  );
};

export const getSejamIsSejamiMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sejamIsSejami>>,
    TError,
    { data: IsSejamiQuery },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sejamIsSejami>>,
  TError,
  { data: IsSejamiQuery },
  TContext
> => {
  const mutationKey = ["sejamIsSejami"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sejamIsSejami>>,
    { data: IsSejamiQuery }
  > = (props) => {
    const { data } = props ?? {};

    return sejamIsSejami(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SejamIsSejamiMutationResult = NonNullable<
  Awaited<ReturnType<typeof sejamIsSejami>>
>;
export type SejamIsSejamiMutationBody = IsSejamiQuery;
export type SejamIsSejamiMutationError = ErrorType<unknown>;

/**
 * @summary چک کردن سجامی بودن کاربر
 */
export const useSejamIsSejami = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof sejamIsSejami>>,
      TError,
      { data: IsSejamiQuery },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof sejamIsSejami>>,
  TError,
  { data: IsSejamiQuery },
  TContext
> => {
  const mutationOptions = getSejamIsSejamiMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ارسال کد یکبار مصرف سجام
 */
export const sejamKycOTP = (
  kycOTPCommand: KycOTPCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Sejam/KycOTP`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: kycOTPCommand,
      signal,
    },
    options,
  );
};

export const getSejamKycOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sejamKycOTP>>,
    TError,
    { data: KycOTPCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sejamKycOTP>>,
  TError,
  { data: KycOTPCommand },
  TContext
> => {
  const mutationKey = ["sejamKycOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sejamKycOTP>>,
    { data: KycOTPCommand }
  > = (props) => {
    const { data } = props ?? {};

    return sejamKycOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SejamKycOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof sejamKycOTP>>
>;
export type SejamKycOTPMutationBody = KycOTPCommand;
export type SejamKycOTPMutationError = ErrorType<unknown>;

/**
 * @summary ارسال کد یکبار مصرف سجام
 */
export const useSejamKycOTP = <TError = ErrorType<unknown>, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof sejamKycOTP>>,
      TError,
      { data: KycOTPCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof sejamKycOTP>>,
  TError,
  { data: KycOTPCommand },
  TContext
> => {
  const mutationOptions = getSejamKycOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary دریافت کلید امنیتی پروفایل سجام
 */
export const sejamProfileInquiry = (
  profileInquiryCommand: ProfileInquiryCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGuid>(
    {
      url: `/api/general/v1/Sejam/ProfileInquiry`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: profileInquiryCommand,
      signal,
    },
    options,
  );
};

export const getSejamProfileInquiryMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sejamProfileInquiry>>,
    TError,
    { data: ProfileInquiryCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sejamProfileInquiry>>,
  TError,
  { data: ProfileInquiryCommand },
  TContext
> => {
  const mutationKey = ["sejamProfileInquiry"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sejamProfileInquiry>>,
    { data: ProfileInquiryCommand }
  > = (props) => {
    const { data } = props ?? {};

    return sejamProfileInquiry(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SejamProfileInquiryMutationResult = NonNullable<
  Awaited<ReturnType<typeof sejamProfileInquiry>>
>;
export type SejamProfileInquiryMutationBody = ProfileInquiryCommand;
export type SejamProfileInquiryMutationError = ErrorType<unknown>;

/**
 * @summary دریافت کلید امنیتی پروفایل سجام
 */
export const useSejamProfileInquiry = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof sejamProfileInquiry>>,
      TError,
      { data: ProfileInquiryCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof sejamProfileInquiry>>,
  TError,
  { data: ProfileInquiryCommand },
  TContext
> => {
  const mutationOptions = getSejamProfileInquiryMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary نمایش پروفایل سجام با کلید امنیتی
 */
export const sejamProfileInquiryShow = (
  params?: SejamProfileInquiryShowParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfProfileResponse>(
    {
      url: `/api/general/v1/Sejam/ProfileInquiryShow`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getSejamProfileInquiryShowQueryKey = (
  params?: SejamProfileInquiryShowParams,
) => {
  return [
    `/api/general/v1/Sejam/ProfileInquiryShow`,
    ...(params ? [params] : []),
  ] as const;
};

export const getSejamProfileInquiryShowQueryOptions = <
  TData = Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
  TError = ErrorType<unknown>,
>(
  params?: SejamProfileInquiryShowParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSejamProfileInquiryShowQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof sejamProfileInquiryShow>>
  > = ({ signal }) => sejamProfileInquiryShow(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type SejamProfileInquiryShowQueryResult = NonNullable<
  Awaited<ReturnType<typeof sejamProfileInquiryShow>>
>;
export type SejamProfileInquiryShowQueryError = ErrorType<unknown>;

export function useSejamProfileInquiryShow<
  TData = Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | SejamProfileInquiryShowParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
          TError,
          Awaited<ReturnType<typeof sejamProfileInquiryShow>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSejamProfileInquiryShow<
  TData = Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
  TError = ErrorType<unknown>,
>(
  params?: SejamProfileInquiryShowParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
          TError,
          Awaited<ReturnType<typeof sejamProfileInquiryShow>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSejamProfileInquiryShow<
  TData = Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
  TError = ErrorType<unknown>,
>(
  params?: SejamProfileInquiryShowParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary نمایش پروفایل سجام با کلید امنیتی
 */

export function useSejamProfileInquiryShow<
  TData = Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
  TError = ErrorType<unknown>,
>(
  params?: SejamProfileInquiryShowParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof sejamProfileInquiryShow>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getSejamProfileInquiryShowQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ایجاد پروفایل کاربری برای ویزیتور
 */
export const sejamSetAsCustomerProfile = (
  setAsCustomerProfileCommand: SetAsCustomerProfileCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Sejam/SetAsCustomerProfile`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: setAsCustomerProfileCommand,
      signal,
    },
    options,
  );
};

export const getSejamSetAsCustomerProfileMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>,
    TError,
    { data: SetAsCustomerProfileCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>,
  TError,
  { data: SetAsCustomerProfileCommand },
  TContext
> => {
  const mutationKey = ["sejamSetAsCustomerProfile"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>,
    { data: SetAsCustomerProfileCommand }
  > = (props) => {
    const { data } = props ?? {};

    return sejamSetAsCustomerProfile(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SejamSetAsCustomerProfileMutationResult = NonNullable<
  Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>
>;
export type SejamSetAsCustomerProfileMutationBody = SetAsCustomerProfileCommand;
export type SejamSetAsCustomerProfileMutationError = ErrorType<unknown>;

/**
 * @summary ایجاد پروفایل کاربری برای ویزیتور
 */
export const useSejamSetAsCustomerProfile = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>,
      TError,
      { data: SetAsCustomerProfileCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof sejamSetAsCustomerProfile>>,
  TError,
  { data: SetAsCustomerProfileCommand },
  TContext
> => {
  const mutationOptions = getSejamSetAsCustomerProfileMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ویرایش پروفایل مشتری
 */
export const sejamUpdateProfile = (
  updateProfileCommand: UpdateProfileCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Sejam/UpdateProfile`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: updateProfileCommand,
    },
    options,
  );
};

export const getSejamUpdateProfileMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sejamUpdateProfile>>,
    TError,
    { data: UpdateProfileCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sejamUpdateProfile>>,
  TError,
  { data: UpdateProfileCommand },
  TContext
> => {
  const mutationKey = ["sejamUpdateProfile"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sejamUpdateProfile>>,
    { data: UpdateProfileCommand }
  > = (props) => {
    const { data } = props ?? {};

    return sejamUpdateProfile(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SejamUpdateProfileMutationResult = NonNullable<
  Awaited<ReturnType<typeof sejamUpdateProfile>>
>;
export type SejamUpdateProfileMutationBody = UpdateProfileCommand;
export type SejamUpdateProfileMutationError = ErrorType<unknown>;

/**
 * @summary ویرایش پروفایل مشتری
 */
export const useSejamUpdateProfile = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof sejamUpdateProfile>>,
      TError,
      { data: UpdateProfileCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof sejamUpdateProfile>>,
  TError,
  { data: UpdateProfileCommand },
  TContext
> => {
  const mutationOptions = getSejamUpdateProfileMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
