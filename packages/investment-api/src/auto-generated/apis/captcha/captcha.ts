/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfCaptchaTypeEnum,
  ApiResultOfRequestCaptchaOutputDTO,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary کدام نوع کپچا فعال است
 */
export const captchaWhichCaptchaIsActive = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfCaptchaTypeEnum>(
    {
      url: `/api/general/v1/Captcha/WhichCaptchaIsActive`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getCaptchaWhichCaptchaIsActiveQueryKey = () => {
  return [`/api/general/v1/Captcha/WhichCaptchaIsActive`] as const;
};

export const getCaptchaWhichCaptchaIsActiveQueryOptions = <
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getCaptchaWhichCaptchaIsActiveQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>
  > = ({ signal }) => captchaWhichCaptchaIsActive(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CaptchaWhichCaptchaIsActiveQueryResult = NonNullable<
  Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>
>;
export type CaptchaWhichCaptchaIsActiveQueryError = ErrorType<unknown>;

export function useCaptchaWhichCaptchaIsActive<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
          TError,
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaWhichCaptchaIsActive<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
          TError,
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaWhichCaptchaIsActive<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary کدام نوع کپچا فعال است
 */

export function useCaptchaWhichCaptchaIsActive<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getCaptchaWhichCaptchaIsActiveQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary درخواست کپچای متنی
 */
export const captchaRequestTextCaptcha = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfRequestCaptchaOutputDTO>(
    {
      url: `/api/general/v1/Captcha/RequestTextCaptcha`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getCaptchaRequestTextCaptchaQueryKey = () => {
  return [`/api/general/v1/Captcha/RequestTextCaptcha`] as const;
};

export const getCaptchaRequestTextCaptchaQueryOptions = <
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getCaptchaRequestTextCaptchaQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof captchaRequestTextCaptcha>>
  > = ({ signal }) => captchaRequestTextCaptcha(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CaptchaRequestTextCaptchaQueryResult = NonNullable<
  Awaited<ReturnType<typeof captchaRequestTextCaptcha>>
>;
export type CaptchaRequestTextCaptchaQueryError = ErrorType<unknown>;

export function useCaptchaRequestTextCaptcha<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
          TError,
          Awaited<ReturnType<typeof captchaRequestTextCaptcha>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaRequestTextCaptcha<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
          TError,
          Awaited<ReturnType<typeof captchaRequestTextCaptcha>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaRequestTextCaptcha<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary درخواست کپچای متنی
 */

export function useCaptchaRequestTextCaptcha<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getCaptchaRequestTextCaptchaQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary کدام نوع کپچا فعال است
 */
export const captchaWhichCaptchaIsActive2 = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfCaptchaTypeEnum>(
    {
      url: `/api/backoffice/v1/Captcha/WhichCaptchaIsActive`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getCaptchaWhichCaptchaIsActive2QueryKey = () => {
  return [`/api/backoffice/v1/Captcha/WhichCaptchaIsActive`] as const;
};

export const getCaptchaWhichCaptchaIsActive2QueryOptions = <
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getCaptchaWhichCaptchaIsActive2QueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>
  > = ({ signal }) => captchaWhichCaptchaIsActive2(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CaptchaWhichCaptchaIsActive2QueryResult = NonNullable<
  Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>
>;
export type CaptchaWhichCaptchaIsActive2QueryError = ErrorType<unknown>;

export function useCaptchaWhichCaptchaIsActive2<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
          TError,
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaWhichCaptchaIsActive2<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
          TError,
          Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaWhichCaptchaIsActive2<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary کدام نوع کپچا فعال است
 */

export function useCaptchaWhichCaptchaIsActive2<
  TData = Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaWhichCaptchaIsActive2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getCaptchaWhichCaptchaIsActive2QueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary درخواست کپچای متنی
 */
export const captchaRequestTextCaptcha2 = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfRequestCaptchaOutputDTO>(
    {
      url: `/api/backoffice/v1/Captcha/RequestTextCaptcha`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getCaptchaRequestTextCaptcha2QueryKey = () => {
  return [`/api/backoffice/v1/Captcha/RequestTextCaptcha`] as const;
};

export const getCaptchaRequestTextCaptcha2QueryOptions = <
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getCaptchaRequestTextCaptcha2QueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>
  > = ({ signal }) => captchaRequestTextCaptcha2(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type CaptchaRequestTextCaptcha2QueryResult = NonNullable<
  Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>
>;
export type CaptchaRequestTextCaptcha2QueryError = ErrorType<unknown>;

export function useCaptchaRequestTextCaptcha2<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
          TError,
          Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaRequestTextCaptcha2<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
          TError,
          Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useCaptchaRequestTextCaptcha2<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary درخواست کپچای متنی
 */

export function useCaptchaRequestTextCaptcha2<
  TData = Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof captchaRequestTextCaptcha2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getCaptchaRequestTextCaptcha2QueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
