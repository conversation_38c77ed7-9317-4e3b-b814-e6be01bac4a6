/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type { ApiResultOfGetLoggedInUserProfileResponse } from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary پروفایل کاربری
 */
export const profileGetLoggedInUserProfile = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetLoggedInUserProfileResponse>(
    { url: `/api/general/v1/Profile`, method: "GET", signal },
    options,
  );
};

export const getProfileGetLoggedInUserProfileQueryKey = () => {
  return [`/api/general/v1/Profile`] as const;
};

export const getProfileGetLoggedInUserProfileQueryOptions = <
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getProfileGetLoggedInUserProfileQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>
  > = ({ signal }) => profileGetLoggedInUserProfile(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type ProfileGetLoggedInUserProfileQueryResult = NonNullable<
  Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>
>;
export type ProfileGetLoggedInUserProfileQueryError = ErrorType<unknown>;

export function useProfileGetLoggedInUserProfile<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
          TError,
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useProfileGetLoggedInUserProfile<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
          TError,
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useProfileGetLoggedInUserProfile<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary پروفایل کاربری
 */

export function useProfileGetLoggedInUserProfile<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getProfileGetLoggedInUserProfileQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary پروفایل کاربری
 */
export const profileGetLoggedInUserProfile2 = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetLoggedInUserProfileResponse>(
    { url: `/api/backoffice/v1/Profile`, method: "GET", signal },
    options,
  );
};

export const getProfileGetLoggedInUserProfile2QueryKey = () => {
  return [`/api/backoffice/v1/Profile`] as const;
};

export const getProfileGetLoggedInUserProfile2QueryOptions = <
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getProfileGetLoggedInUserProfile2QueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>
  > = ({ signal }) => profileGetLoggedInUserProfile2(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type ProfileGetLoggedInUserProfile2QueryResult = NonNullable<
  Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>
>;
export type ProfileGetLoggedInUserProfile2QueryError = ErrorType<unknown>;

export function useProfileGetLoggedInUserProfile2<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
          TError,
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useProfileGetLoggedInUserProfile2<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
          TError,
          Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useProfileGetLoggedInUserProfile2<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary پروفایل کاربری
 */

export function useProfileGetLoggedInUserProfile2<
  TData = Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof profileGetLoggedInUserProfile2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getProfileGetLoggedInUserProfile2QueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
