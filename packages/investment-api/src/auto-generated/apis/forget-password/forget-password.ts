/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation } from "@tanstack/react-query";
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfGetOtpWithSecretKeyResponse,
  ApiResultOfGuid,
  ForgetPasswordCheckOTPCommand,
  ForgetPasswordSendOTPCommand,
  ForgetPasswordSetNewPasswordCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary فراموشی کلمه عبور - ارسال کد یکبار مصرف
 */
export const forgetPasswordSendOTP = (
  forgetPasswordSendOTPCommand: ForgetPasswordSendOTPCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetOtpWithSecretKeyResponse>(
    {
      url: `/api/customer/v1/ForgetPassword/SendOTP`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: forgetPasswordSendOTPCommand,
      signal,
    },
    options,
  );
};

export const getForgetPasswordSendOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof forgetPasswordSendOTP>>,
    TError,
    { data: ForgetPasswordSendOTPCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof forgetPasswordSendOTP>>,
  TError,
  { data: ForgetPasswordSendOTPCommand },
  TContext
> => {
  const mutationKey = ["forgetPasswordSendOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof forgetPasswordSendOTP>>,
    { data: ForgetPasswordSendOTPCommand }
  > = (props) => {
    const { data } = props ?? {};

    return forgetPasswordSendOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ForgetPasswordSendOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof forgetPasswordSendOTP>>
>;
export type ForgetPasswordSendOTPMutationBody = ForgetPasswordSendOTPCommand;
export type ForgetPasswordSendOTPMutationError = ErrorType<unknown>;

/**
 * @summary فراموشی کلمه عبور - ارسال کد یکبار مصرف
 */
export const useForgetPasswordSendOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof forgetPasswordSendOTP>>,
      TError,
      { data: ForgetPasswordSendOTPCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof forgetPasswordSendOTP>>,
  TError,
  { data: ForgetPasswordSendOTPCommand },
  TContext
> => {
  const mutationOptions = getForgetPasswordSendOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary فراموشی کلمه عبور - چک کردن کد یکبار مصرف
 */
export const forgetPasswordCheckOTP = (
  forgetPasswordCheckOTPCommand: ForgetPasswordCheckOTPCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGuid>(
    {
      url: `/api/customer/v1/ForgetPassword/CheckOTP`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: forgetPasswordCheckOTPCommand,
      signal,
    },
    options,
  );
};

export const getForgetPasswordCheckOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof forgetPasswordCheckOTP>>,
    TError,
    { data: ForgetPasswordCheckOTPCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof forgetPasswordCheckOTP>>,
  TError,
  { data: ForgetPasswordCheckOTPCommand },
  TContext
> => {
  const mutationKey = ["forgetPasswordCheckOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof forgetPasswordCheckOTP>>,
    { data: ForgetPasswordCheckOTPCommand }
  > = (props) => {
    const { data } = props ?? {};

    return forgetPasswordCheckOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ForgetPasswordCheckOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof forgetPasswordCheckOTP>>
>;
export type ForgetPasswordCheckOTPMutationBody = ForgetPasswordCheckOTPCommand;
export type ForgetPasswordCheckOTPMutationError = ErrorType<unknown>;

/**
 * @summary فراموشی کلمه عبور - چک کردن کد یکبار مصرف
 */
export const useForgetPasswordCheckOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof forgetPasswordCheckOTP>>,
      TError,
      { data: ForgetPasswordCheckOTPCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof forgetPasswordCheckOTP>>,
  TError,
  { data: ForgetPasswordCheckOTPCommand },
  TContext
> => {
  const mutationOptions = getForgetPasswordCheckOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary فراموشی کلمه عبور - تنظیم کلمه عبور جدید
 */
export const forgetPasswordSetNewPassword = (
  forgetPasswordSetNewPasswordCommand: ForgetPasswordSetNewPasswordCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGuid>(
    {
      url: `/api/customer/v1/ForgetPassword/SetNewPassword`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: forgetPasswordSetNewPasswordCommand,
      signal,
    },
    options,
  );
};

export const getForgetPasswordSetNewPasswordMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>,
    TError,
    { data: ForgetPasswordSetNewPasswordCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>,
  TError,
  { data: ForgetPasswordSetNewPasswordCommand },
  TContext
> => {
  const mutationKey = ["forgetPasswordSetNewPassword"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>,
    { data: ForgetPasswordSetNewPasswordCommand }
  > = (props) => {
    const { data } = props ?? {};

    return forgetPasswordSetNewPassword(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ForgetPasswordSetNewPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>
>;
export type ForgetPasswordSetNewPasswordMutationBody =
  ForgetPasswordSetNewPasswordCommand;
export type ForgetPasswordSetNewPasswordMutationError = ErrorType<unknown>;

/**
 * @summary فراموشی کلمه عبور - تنظیم کلمه عبور جدید
 */
export const useForgetPasswordSetNewPassword = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>,
      TError,
      { data: ForgetPasswordSetNewPasswordCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof forgetPasswordSetNewPassword>>,
  TError,
  { data: ForgetPasswordSetNewPasswordCommand },
  TContext
> => {
  const mutationOptions =
    getForgetPasswordSetNewPasswordMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
