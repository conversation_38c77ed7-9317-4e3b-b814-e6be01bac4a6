/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfSystemSettingsResponse,
  EditSystemSettingsCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت تنظیمات سیستم
 */
export const settingsGetSystemSettings = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfSystemSettingsResponse>(
    {
      url: `/api/backoffice/v1/Settings/GetSystemSettings`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getSettingsGetSystemSettingsQueryKey = () => {
  return [`/api/backoffice/v1/Settings/GetSystemSettings`] as const;
};

export const getSettingsGetSystemSettingsQueryOptions = <
  TData = Awaited<ReturnType<typeof settingsGetSystemSettings>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof settingsGetSystemSettings>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSettingsGetSystemSettingsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof settingsGetSystemSettings>>
  > = ({ signal }) => settingsGetSystemSettings(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof settingsGetSystemSettings>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type SettingsGetSystemSettingsQueryResult = NonNullable<
  Awaited<ReturnType<typeof settingsGetSystemSettings>>
>;
export type SettingsGetSystemSettingsQueryError = ErrorType<unknown>;

export function useSettingsGetSystemSettings<
  TData = Awaited<ReturnType<typeof settingsGetSystemSettings>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof settingsGetSystemSettings>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof settingsGetSystemSettings>>,
          TError,
          Awaited<ReturnType<typeof settingsGetSystemSettings>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSettingsGetSystemSettings<
  TData = Awaited<ReturnType<typeof settingsGetSystemSettings>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof settingsGetSystemSettings>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof settingsGetSystemSettings>>,
          TError,
          Awaited<ReturnType<typeof settingsGetSystemSettings>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSettingsGetSystemSettings<
  TData = Awaited<ReturnType<typeof settingsGetSystemSettings>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof settingsGetSystemSettings>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت تنظیمات سیستم
 */

export function useSettingsGetSystemSettings<
  TData = Awaited<ReturnType<typeof settingsGetSystemSettings>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof settingsGetSystemSettings>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getSettingsGetSystemSettingsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ویرایش تنظیمات سیستم
 */
export const settingsEditSystemSettings = (
  editSystemSettingsCommand: EditSystemSettingsCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Settings/EditSystemSettings`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editSystemSettingsCommand,
    },
    options,
  );
};

export const getSettingsEditSystemSettingsMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof settingsEditSystemSettings>>,
    TError,
    { data: EditSystemSettingsCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof settingsEditSystemSettings>>,
  TError,
  { data: EditSystemSettingsCommand },
  TContext
> => {
  const mutationKey = ["settingsEditSystemSettings"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof settingsEditSystemSettings>>,
    { data: EditSystemSettingsCommand }
  > = (props) => {
    const { data } = props ?? {};

    return settingsEditSystemSettings(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SettingsEditSystemSettingsMutationResult = NonNullable<
  Awaited<ReturnType<typeof settingsEditSystemSettings>>
>;
export type SettingsEditSystemSettingsMutationBody = EditSystemSettingsCommand;
export type SettingsEditSystemSettingsMutationError = ErrorType<unknown>;

/**
 * @summary ویرایش تنظیمات سیستم
 */
export const useSettingsEditSystemSettings = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof settingsEditSystemSettings>>,
      TError,
      { data: EditSystemSettingsCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof settingsEditSystemSettings>>,
  TError,
  { data: EditSystemSettingsCommand },
  TContext
> => {
  const mutationOptions = getSettingsEditSystemSettingsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
