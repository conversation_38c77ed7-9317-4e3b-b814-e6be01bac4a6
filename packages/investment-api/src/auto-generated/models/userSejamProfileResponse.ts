/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { SejamPrivatePersonResponse } from "./sejamPrivatePersonResponse";
import type { SejamAddressResponse } from "./sejamAddressResponse";
import type { SejamTradingCodeResponse } from "./sejamTradingCodeResponse";
import type { SejamBankingAccountResponse } from "./sejamBankingAccountResponse";
import type { SejamJobInfoResponse } from "./sejamJobInfoResponse";
import type { SejamFinancialInfoResponse } from "./sejamFinancialInfoResponse";

export interface UserSejamProfileResponse {
  createDate?: string;
  mobile?: number;
  /** @nullable */
  email?: string | null;
  /** @nullable */
  uniqueIdentifier?: string | null;
  /** @nullable */
  type?: string | null;
  /** @nullable */
  status?: string | null;
  privatePerson?: SejamPrivatePersonResponse;
  addresses?: SejamAddressResponse[];
  tradingCodes?: SejamTradingCodeResponse[];
  accounts?: SejamBankingAccountResponse[];
  jobInfo?: SejamJobInfoResponse;
  financialInfo?: SejamFinancialInfoResponse;
}
