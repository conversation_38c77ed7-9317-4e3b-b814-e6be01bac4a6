/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { RoleEnum } from "./roleEnum";
import type { UserSystemProfileResponse } from "./userSystemProfileResponse";
import type { UserSejamProfileResponse } from "./userSejamProfileResponse";

export interface UserProfileResponse {
  userId?: string;
  createDate?: string;
  userName?: string;
  mobileNumber?: string;
  isActive?: boolean;
  role?: RoleEnum;
  roleName?: string;
  roleDescription?: string;
  userSystemProfile?: UserSystemProfileResponse;
  userSejamProfile?: UserSejamProfileResponse;
}
