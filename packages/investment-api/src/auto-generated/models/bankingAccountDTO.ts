/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { BankDTO } from "./bankDTO";
import type { BranchCityDTO } from "./branchCityDTO";

export interface BankingAccountDTO {
  /** @nullable */
  accountNumber?: string | null;
  /** @nullable */
  type?: string | null;
  /** @nullable */
  sheba?: string | null;
  bank?: BankDTO;
  /** @nullable */
  branchCode?: string | null;
  /** @nullable */
  branchName?: string | null;
  branchCity?: BranchCityDTO;
  /** @nullable */
  isDefault?: boolean | null;
  /** @nullable */
  modifiedDate?: string | null;
}
