/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { GetAllFinancialInstitutesResponse } from "./getAllFinancialInstitutesResponse";

export interface ApiResultOfListOfGetAllFinancialInstitutesResponse {
  /** @nullable */
  data?: GetAllFinancialInstitutesResponse[] | null;
  isSuccess?: boolean;
  /** @nullable */
  errorCode?: string | null;
  /** @nullable */
  errorMessage?: string | null;
}
