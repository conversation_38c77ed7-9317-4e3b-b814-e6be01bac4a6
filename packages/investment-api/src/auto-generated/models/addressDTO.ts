/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { CountryDTO } from "./countryDTO";
import type { ProvinceDTO } from "./provinceDTO";
import type { CityDTO } from "./cityDTO";
import type { SectionDTO } from "./sectionDTO";

export interface AddressDTO {
  /** @nullable */
  postalCode?: string | null;
  country?: CountryDTO;
  province?: ProvinceDTO;
  city?: CityDTO;
  section?: SectionDTO;
  /** @nullable */
  cityPrefix?: string | null;
  /** @nullable */
  remnantAddress?: string | null;
  /** @nullable */
  alley?: string | null;
  /** @nullable */
  plaque?: string | null;
  /** @nullable */
  tel?: string | null;
  /** @nullable */
  countryPrefix?: string | null;
  /** @nullable */
  mobile?: string | null;
  /** @nullable */
  emergencyTel?: string | null;
  /** @nullable */
  emergencyTelCityPrefix?: string | null;
  /** @nullable */
  emergencyTelCountryPrefix?: string | null;
  /** @nullable */
  faxPrefix?: string | null;
  /** @nullable */
  fax?: string | null;
  /** @nullable */
  website?: string | null;
  /** @nullable */
  email?: string | null;
}
