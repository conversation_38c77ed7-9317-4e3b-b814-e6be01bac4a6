/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { RoleEnum } from "./roleEnum";
import type { GetLoggedInUserProfileResponseUserProfile } from "./getLoggedInUserProfileResponseUserProfile";
import type { GetLoggedInUserProfileResponseVisitorUserProfile } from "./getLoggedInUserProfileResponseVisitorUserProfile";

export interface GetLoggedInUserProfileResponse {
  role?: RoleEnum;
  /** @nullable */
  userProfile?: GetLoggedInUserProfileResponseUserProfile;
  /** @nullable */
  visitorUserProfile?: GetLoggedInUserProfileResponseVisitorUserProfile;
}
