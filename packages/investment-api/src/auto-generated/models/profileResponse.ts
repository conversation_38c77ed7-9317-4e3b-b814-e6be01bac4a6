/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { PrivatePersonDTO } from "./privatePersonDTO";
import type { ProfileResponseLegalPerson } from "./profileResponseLegalPerson";
import type { AddressDTO } from "./addressDTO";
import type { TradingCodeDTO } from "./tradingCodeDTO";
import type { ProfileResponseAgent } from "./profileResponseAgent";
import type { BankingAccountDTO } from "./bankingAccountDTO";
import type { JobInfoDTO } from "./jobInfoDTO";
import type { FinancialInfoDTO } from "./financialInfoDTO";

export interface ProfileResponse {
  mobile?: number;
  /** @nullable */
  email?: string | null;
  /** @nullable */
  uniqueIdentifier?: string | null;
  /** @nullable */
  type?: string | null;
  /** @nullable */
  status?: string | null;
  privatePerson?: PrivatePersonDTO;
  /** @nullable */
  legalPerson?: ProfileResponseLegalPerson;
  addresses?: AddressDTO[];
  tradingCodes?: TradingCodeDTO[];
  /** @nullable */
  agent?: ProfileResponseAgent;
  accounts?: BankingAccountDTO[];
  jobInfo?: JobInfoDTO;
  financialInfo?: FinancialInfoDTO;
  legalPersonShareholders?: unknown[];
  legalPersonStakeholders?: unknown[];
}
