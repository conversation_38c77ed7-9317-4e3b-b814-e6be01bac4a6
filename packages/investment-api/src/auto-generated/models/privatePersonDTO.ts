/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { PrivatePersonDTOSignatureFile } from "./privatePersonDTOSignatureFile";

export interface PrivatePersonDTO {
  /** @nullable */
  firstName?: string | null;
  /** @nullable */
  lastName?: string | null;
  /** @nullable */
  fatherName?: string | null;
  /** @nullable */
  gender?: string | null;
  /** @nullable */
  seriShChar?: string | null;
  /** @nullable */
  seriSh?: string | null;
  /** @nullable */
  serial?: string | null;
  /** @nullable */
  shNumber?: string | null;
  /** @nullable */
  birthDate?: string | null;
  /** @nullable */
  placeOfIssue?: string | null;
  /** @nullable */
  placeOfBirth?: string | null;
  /** @nullable */
  signatureFile?: PrivatePersonDTOSignatureFile;
}
