/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import type { JobDTO } from "./jobDTO";

export interface JobInfoDTO {
  /** @nullable */
  employmentDate?: string | null;
  /** @nullable */
  companyName?: string | null;
  /** @nullable */
  companyAddress?: string | null;
  /** @nullable */
  companyPostalCode?: string | null;
  /** @nullable */
  companyEmail?: string | null;
  /** @nullable */
  companyWebSite?: string | null;
  /** @nullable */
  companyCityPrefix?: string | null;
  /** @nullable */
  companyPhone?: string | null;
  /** @nullable */
  position?: string | null;
  /** @nullable */
  companyFaxPrefix?: string | null;
  /** @nullable */
  companyFax?: string | null;
  job?: JobDTO;
  /** @nullable */
  jobDescription?: string | null;
}
