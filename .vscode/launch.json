{"version": "0.2.0", "inputs": [{"id": "selectProject", "type": "pickString", "description": "Select a project to debug", "options": ["admin", "web", "storybook"]}], "configurations": [{"name": "Debug ${input:selectProject}", "type": "node", "request": "launch", "runtimeExecutable": "pnpm", "program": "${workspaceFolder}/node_modules/.bin/turbo", "args": ["dev", "-F", "${input:selectProject}"], "skipFiles": ["<node_internals>/**"], "cwd": "${workspaceFolder}/apps/${input:selectProject}", "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect"}}]}